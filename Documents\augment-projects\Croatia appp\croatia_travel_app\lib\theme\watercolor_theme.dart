import 'package:flutter/material.dart';

/// 🎨 ADRIATIC DIARY - Sjednocený Design Systém
/// Kombinuje chorvatskou identitu (moře + západ slunce) s watercolor estetikou
class WatercolorTheme {
  // 🌊 PRIMÁRNÍ CHORVATSKÉ BARVY (z DESIGN_SYSTEM.md)
  static const Color adriaticBlue = Color(0xFF006994); // Jaderská modrá - hlavní brand
  static const Color mediterraneanTeal = Color(0xFF2E8B8B); // Středomořská tyrkysová
  static const Color sunsetOrange = Color(0xFFFF6B35); // Oranžová západ slunce

  // 🏖️ NEUTRÁLNÍ PÍSKOVNÉ BARVY (vylepšené)
  static const Color creamPaper = Color(0xFFF8F6F0); // Krémový papír - hlavní pozadí
  static const Color pureWhite = Color(0xFFFFFFFF); // Čistá bílá - karty
  static const Color charcoal = Color(0xFF2C2C2C); // D<PERSON><PERSON><PERSON>n<PERSON> uhlí - hlavní text
  static const Color border = Color(0xFFE5DDD1); // Jemná hranice
  static const Color shadow = Color(0xFFE5DDD1); // Jemný stín

  // 🎨 POMOCNÉ BARVY (pro různé odstíny)
  static const Color surface = Color(0xFFFAF8F5); // Velmi světlá písková
  static const Color cardBackground = Color(0xFFFCFBF8); // Nejsvětlejší písková
  static const Color accent = Color(0xFFD4C4A8); // Světlejší písková pro akcenty

  // ⚡ KOMPATIBILITA - mapování starých názvů na nové barvy
  static const Color background = creamPaper;
  static const Color primary = adriaticBlue;
  static const Color secondary = mediterraneanTeal;
  static const Color deepNavy = adriaticBlue;
  static const Color elegantTeal = mediterraneanTeal;
  static const Color warmCream = creamPaper;
  static const Color softPeach = surface;
  static const Color richCharcoal = charcoal;
  static const Color luxuryGold = accent;
  static const Color subtleGray = surface;
  static const Color coastalMist = surface;

  // Minimalistické písková gradienty
  static const List<Color> adriaticDreamGradient = [
    background,
    surface,
    cardBackground,
    pureWhite,
  ];

  static const List<Color> coastalMistGradient = [
    background,
    surface,
    cardBackground,
    pureWhite,
  ];

  static const List<Color> elegantTealGradient = [
    surface,
    cardBackground,
    pureWhite,
  ];

  static const List<Color> luxuryGoldGradient = [accent, surface, background];

  static const List<Color> deepNavyGradient = [primary, secondary, accent];

  // Kompatibilita se starými názvy gradientů
  static const List<Color> adriaticGradient = adriaticDreamGradient;
  static const List<Color> mediterraneanGradient = elegantTealGradient;
  static const List<Color> sunsetGradient = luxuryGoldGradient;
  static const List<Color> paperGradient = coastalMistGradient;

  // Minimalistické jemné stíny
  static const List<BoxShadow> watercolorShadow = [
    BoxShadow(
      color: Color(0x1AE5DDD1),
      blurRadius: 8,
      offset: Offset(0, 4),
      spreadRadius: 2,
    ),
    BoxShadow(
      color: Color(0x0DE5DDD1),
      blurRadius: 16,
      offset: Offset(0, 8),
      spreadRadius: 4,
    ),
  ];

  static const List<BoxShadow> softWatercolorShadow = [
    BoxShadow(
      color: Color(0x0FE5DDD1),
      blurRadius: 12,
      offset: Offset(0, 2),
      spreadRadius: 1,
    ),
  ];

  // Minimalistická typography - čisté a jednoduché
  static const TextStyle headingLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 32,
    fontWeight: FontWeight.w300,
    color: primary,
    height: 1.2,
    letterSpacing: 2.0,
  );

  static const TextStyle headingMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 24,
    fontWeight: FontWeight.w400,
    color: primary,
    height: 1.3,
    letterSpacing: 1.0,
  );

  static const TextStyle headingSmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 20,
    fontWeight: FontWeight.w400,
    color: primary,
    height: 1.4,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: secondary,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: secondary,
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: secondary,
    height: 1.4,
  );

  static const TextStyle labelLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: primary,
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: primary,
    height: 1.3,
  );

  // Minimalistické poznámky
  static const TextStyle handwrittenNote = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: secondary,
    height: 1.6,
    fontStyle: FontStyle.italic,
  );

  static const TextStyle handwrittenTitle = TextStyle(
    fontFamily: 'Inter',
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: primary,
    height: 1.5,
  );

  // Watercolor border radius
  static const BorderRadius softRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius mediumRadius = BorderRadius.all(
    Radius.circular(16),
  );
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(24));

  // Watercolor spacing
  static const double spacingXS = 4;
  static const double spacingS = 8;
  static const double spacingM = 16;
  static const double spacingL = 24;
  static const double spacingXL = 32;
  static const double spacingXXL = 48;

  // Watercolor animace
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  // Watercolor curves
  static const Curve watercolorCurve = Curves.easeOutCubic;
  static const Curve softCurve = Curves.easeInOutQuart;

  /// Vytvoří watercolor gradient
  static LinearGradient createWatercolorGradient(
    List<Color> colors, {
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: stops ?? _generateStops(colors.length),
    );
  }

  /// Vytvoří watercolor radial gradient
  static RadialGradient createWatercolorRadial(
    List<Color> colors, {
    Alignment center = Alignment.center,
    double radius = 0.8,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
      stops: _generateStops(colors.length),
    );
  }

  /// Generuje stops pro gradient
  static List<double> _generateStops(int colorCount) {
    if (colorCount <= 1) return [0.0];

    final stops = <double>[];
    for (int i = 0; i < colorCount; i++) {
      stops.add(i / (colorCount - 1));
    }
    return stops;
  }

  /// Watercolor button style
  static ButtonStyle watercolorButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
    Size? minimumSize,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? adriaticBlue,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: 0,
      shadowColor: Colors.transparent,
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      minimumSize: minimumSize ?? const Size(120, 44),
      shape: RoundedRectangleBorder(borderRadius: softRadius),
    ).copyWith(
      overlayColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return (backgroundColor ?? adriaticBlue).withValues(alpha: 0.1);
        }
        if (states.contains(WidgetState.hovered)) {
          return (backgroundColor ?? adriaticBlue).withValues(alpha: 0.05);
        }
        return null;
      }),
    );
  }

  /// Watercolor card decoration
  static BoxDecoration watercolorCardDecoration({
    Color? color,
    List<Color>? gradient,
    List<BoxShadow>? shadows,
    BorderRadius? borderRadius,
  }) {
    return BoxDecoration(
      color: gradient == null ? (color ?? creamPaper) : null,
      gradient: gradient != null ? createWatercolorGradient(gradient) : null,
      borderRadius: borderRadius ?? softRadius,
      boxShadow: shadows ?? softWatercolorShadow,
    );
  }

  /// Watercolor input decoration
  static InputDecoration watercolorInputDecoration({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: creamPaper,
      border: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: BorderSide(color: adriaticBlue.withValues(alpha: 0.2)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: BorderSide(color: adriaticBlue.withValues(alpha: 0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: const BorderSide(color: adriaticBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: const BorderSide(color: Colors.red, width: 1),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: bodyMedium.copyWith(color: charcoal.withValues(alpha: 0.6)),
      labelStyle: labelMedium,
    );
  }

  /// Watercolor app bar theme
  static AppBarTheme get watercolorAppBarTheme => AppBarTheme(
    backgroundColor: Colors.transparent,
    elevation: 0,
    scrolledUnderElevation: 0,
    centerTitle: true,
    titleTextStyle: headingMedium.copyWith(color: adriaticBlue),
    iconTheme: const IconThemeData(color: adriaticBlue),
    actionsIconTheme: const IconThemeData(color: adriaticBlue),
  );

  /// Kompletní watercolor theme
  static ThemeData get watercolorThemeData => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: adriaticBlue,
      brightness: Brightness.light,
      surface: creamPaper,
      onSurface: charcoal,
    ),
    scaffoldBackgroundColor: creamPaper,
    appBarTheme: watercolorAppBarTheme,
    textTheme: TextTheme(
      headlineLarge: headingLarge,
      headlineMedium: headingMedium,
      headlineSmall: headingSmall,
      bodyLarge: bodyLarge,
      bodyMedium: bodyMedium,
      bodySmall: bodySmall,
      labelLarge: labelLarge,
      labelMedium: labelMedium,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: watercolorButtonStyle(),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: creamPaper,
      border: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: BorderSide(color: adriaticBlue.withValues(alpha: 0.2)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: BorderSide(color: adriaticBlue.withValues(alpha: 0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: const BorderSide(color: adriaticBlue, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: bodyMedium.copyWith(color: charcoal.withValues(alpha: 0.6)),
      labelStyle: labelMedium,
    ),
    cardTheme: CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: softRadius),
      color: Colors.white,
      shadowColor: adriaticBlue.withValues(alpha: 0.1),
    ),
    dividerTheme: DividerThemeData(
      color: adriaticBlue.withValues(alpha: 0.1),
      thickness: 1,
      space: 1,
    ),
  );
}
