import 'package:flutter/material.dart';

/// Watercolor design systém pro Croatia Travel App
class WatercolorTheme {
  // Watercolor barvy inspirované vaším designem
  static const Color adriaticBlue = Color(0xFF006994);      // Jaderská modrá
  static const Color mediterraneanTeal = Color(0xFF2E8B8B);  // Tyrkysová
  static const Color sunsetOrange = Color(0xFF FF6B35);      // Oranžová
  static const Color creamPaper = Color(0xFFFFF8E7);        // Krémový papír
  static const Color charcoal = Color(0xFF2C2C2C);          // Uhlová

  // Watercolor gradient kombinace
  static const List<Color> adriaticGradient = [
    Color(0xFF006994),
    Color(0xFF4A9BC7),
    Color(0xFF7BB3D3),
  ];

  static const List<Color> mediterraneanGradient = [
    Color(0xFF2E8B8B),
    Color(0xFF5BA3A3),
    Color(0xFF88BBBB),
  ];

  static const List<Color> sunsetGradient = [
    Color(0xFFFF6B35),
    Color(0xFFFF8A5B),
    Color(0xFFFFA981),
  ];

  static const List<Color> paperGradient = [
    Color(0xFFFFF8E7),
    Color(0xFFFFFBF0),
    Color(0xFFFFFEF9),
  ];

  // Watercolor shadows a efekty
  static const List<BoxShadow> watercolorShadow = [
    BoxShadow(
      color: Color(0x1A006994),
      blurRadius: 8,
      offset: Offset(0, 4),
      spreadRadius: 2,
    ),
    BoxShadow(
      color: Color(0x0D006994),
      blurRadius: 16,
      offset: Offset(0, 8),
      spreadRadius: 4,
    ),
  ];

  static const List<BoxShadow> softWatercolorShadow = [
    BoxShadow(
      color: Color(0x0F006994),
      blurRadius: 12,
      offset: Offset(0, 2),
      spreadRadius: 1,
    ),
  ];

  // Typography - Playfair Display pro nadpisy, Inter pro text
  static const TextStyle headingLarge = TextStyle(
    fontFamily: 'Playfair Display',
    fontSize: 32,
    fontWeight: FontWeight.w700,
    color: adriaticBlue,
    height: 1.2,
    letterSpacing: -0.5,
  );

  static const TextStyle headingMedium = TextStyle(
    fontFamily: 'Playfair Display',
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: adriaticBlue,
    height: 1.3,
    letterSpacing: -0.3,
  );

  static const TextStyle headingSmall = TextStyle(
    fontFamily: 'Playfair Display',
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: adriaticBlue,
    height: 1.4,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: charcoal,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: charcoal,
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: charcoal,
    height: 1.4,
  );

  static const TextStyle labelLarge = TextStyle(
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: adriaticBlue,
    height: 1.4,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: adriaticBlue,
    height: 1.3,
  );

  // Watercolor dekorace pro Kalam font (ručně psané poznámky)
  static const TextStyle handwrittenNote = TextStyle(
    fontFamily: 'Kalam',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: mediterraneanTeal,
    height: 1.6,
  );

  static const TextStyle handwrittenTitle = TextStyle(
    fontFamily: 'Kalam',
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: sunsetOrange,
    height: 1.5,
  );

  // Watercolor border radius
  static const BorderRadius softRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius mediumRadius = BorderRadius.all(Radius.circular(16));
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(24));

  // Watercolor spacing
  static const double spacingXS = 4;
  static const double spacingS = 8;
  static const double spacingM = 16;
  static const double spacingL = 24;
  static const double spacingXL = 32;
  static const double spacingXXL = 48;

  // Watercolor animace
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  // Watercolor curves
  static const Curve watercolorCurve = Curves.easeOutCubic;
  static const Curve softCurve = Curves.easeInOutQuart;

  /// Vytvoří watercolor gradient
  static LinearGradient createWatercolorGradient(
    List<Color> colors, {
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: stops ?? _generateStops(colors.length),
    );
  }

  /// Vytvoří watercolor radial gradient
  static RadialGradient createWatercolorRadial(
    List<Color> colors, {
    Alignment center = Alignment.center,
    double radius = 0.8,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
      stops: _generateStops(colors.length),
    );
  }

  /// Generuje stops pro gradient
  static List<double> _generateStops(int colorCount) {
    if (colorCount <= 1) return [0.0];
    
    final stops = <double>[];
    for (int i = 0; i < colorCount; i++) {
      stops.add(i / (colorCount - 1));
    }
    return stops;
  }

  /// Watercolor button style
  static ButtonStyle watercolorButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
    Size? minimumSize,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? adriaticBlue,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: 0,
      shadowColor: Colors.transparent,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      minimumSize: minimumSize ?? const Size(120, 44),
      shape: RoundedRectangleBorder(
        borderRadius: softRadius,
      ),
    ).copyWith(
      overlayColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return (backgroundColor ?? adriaticBlue).withValues(alpha: 0.1);
        }
        if (states.contains(WidgetState.hovered)) {
          return (backgroundColor ?? adriaticBlue).withValues(alpha: 0.05);
        }
        return null;
      }),
    );
  }

  /// Watercolor card decoration
  static BoxDecoration watercolorCardDecoration({
    Color? color,
    List<Color>? gradient,
    List<BoxShadow>? shadows,
    BorderRadius? borderRadius,
  }) {
    return BoxDecoration(
      color: gradient == null ? (color ?? creamPaper) : null,
      gradient: gradient != null ? createWatercolorGradient(gradient) : null,
      borderRadius: borderRadius ?? softRadius,
      boxShadow: shadows ?? softWatercolorShadow,
    );
  }

  /// Watercolor input decoration
  static InputDecoration watercolorInputDecoration({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: creamPaper,
      border: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: BorderSide(color: adriaticBlue.withValues(alpha: 0.2)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: BorderSide(color: adriaticBlue.withValues(alpha: 0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: const BorderSide(color: adriaticBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: softRadius,
        borderSide: const BorderSide(color: Colors.red, width: 1),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: bodyMedium.copyWith(color: charcoal.withValues(alpha: 0.6)),
      labelStyle: labelMedium,
    );
  }

  /// Watercolor app bar theme
  static AppBarTheme get watercolorAppBarTheme => AppBarTheme(
    backgroundColor: Colors.transparent,
    elevation: 0,
    scrolledUnderElevation: 0,
    centerTitle: true,
    titleTextStyle: headingMedium.copyWith(color: adriaticBlue),
    iconTheme: const IconThemeData(color: adriaticBlue),
    actionsIconTheme: const IconThemeData(color: adriaticBlue),
  );

  /// Kompletní watercolor theme
  static ThemeData get watercolorThemeData => ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: adriaticBlue,
      brightness: Brightness.light,
      surface: creamPaper,
      onSurface: charcoal,
    ),
    scaffoldBackgroundColor: creamPaper,
    appBarTheme: watercolorAppBarTheme,
    textTheme: TextTheme(
      headlineLarge: headingLarge,
      headlineMedium: headingMedium,
      headlineSmall: headingSmall,
      bodyLarge: bodyLarge,
      bodyMedium: bodyMedium,
      bodySmall: bodySmall,
      labelLarge: labelLarge,
      labelMedium: labelMedium,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: watercolorButtonStyle(),
    ),
    inputDecorationTheme: watercolorInputDecoration(),
    cardTheme: CardTheme(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: softRadius),
      color: Colors.white,
      shadowColor: adriaticBlue.withValues(alpha: 0.1),
    ),
    dividerTheme: DividerThemeData(
      color: adriaticBlue.withValues(alpha: 0.1),
      thickness: 1,
      space: 1,
    ),
  );
}
