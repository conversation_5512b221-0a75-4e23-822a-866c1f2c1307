import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Watercolor styl ikona s ručně malovaným efektem
class WatercolorIcon extends StatelessWidget {
  final IconData icon;
  final double size;
  final Color color;
  final Color? backgroundColor;
  final bool hasBackground;
  final String? semanticLabel;

  const WatercolorIcon({
    super.key,
    required this.icon,
    this.size = 24,
    this.color = const Color(0xFF006994),
    this.backgroundColor,
    this.hasBackground = false,
    this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      child: CustomPaint(
        size: Size(size + (hasBackground ? 16 : 0), size + (hasBackground ? 16 : 0)),
        painter: WatercolorIconPainter(
          icon: icon,
          iconSize: size,
          iconColor: color,
          backgroundColor: backgroundColor ?? color.withValues(alpha: 0.1),
          hasBackground: hasBackground,
        ),
      ),
    );
  }
}

/// Custom painter pro watercolor efekt ikon
class WatercolorIconPainter extends CustomPainter {
  final IconData icon;
  final double iconSize;
  final Color iconColor;
  final Color backgroundColor;
  final bool hasBackground;

  WatercolorIconPainter({
    required this.icon,
    required this.iconSize,
    required this.iconColor,
    required this.backgroundColor,
    required this.hasBackground,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    if (hasBackground) {
      _paintWatercolorBackground(canvas, center, size);
    }
    
    _paintWatercolorIcon(canvas, center);
  }

  void _paintWatercolorBackground(Canvas canvas, Offset center, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

    // Hlavní watercolor skvrna
    final mainRadius = (iconSize + 16) / 2;
    
    // Více vrstev pro watercolor efekt
    final layers = [
      (backgroundColor.withValues(alpha: 0.15), mainRadius * 1.2),
      (backgroundColor.withValues(alpha: 0.25), mainRadius * 1.0),
      (backgroundColor.withValues(alpha: 0.35), mainRadius * 0.8),
    ];

    for (final (color, radius) in layers) {
      paint.color = color;
      
      // Nepravidelný tvar místo kruhu
      final path = Path();
      final points = <Offset>[];
      
      for (int i = 0; i < 12; i++) {
        final angle = (i * 30) * math.pi / 180;
        final variation = 0.8 + (math.sin(i * 0.7) * 0.3);
        final r = radius * variation;
        points.add(Offset(
          center.dx + r * math.cos(angle),
          center.dy + r * math.sin(angle),
        ));
      }
      
      path.moveTo(points[0].dx, points[0].dy);
      for (int i = 1; i < points.length; i++) {
        final cp1 = Offset(
          (points[i-1].dx + points[i].dx) / 2,
          (points[i-1].dy + points[i].dy) / 2,
        );
        path.quadraticBezierTo(points[i-1].dx, points[i-1].dy, cp1.dx, cp1.dy);
      }
      path.close();
      
      canvas.drawPath(path, paint);
    }
  }

  void _paintWatercolorIcon(Canvas canvas, Offset center) {
    // Watercolor efekt pro ikonu
    final iconPaint = Paint()
      ..color = iconColor
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 0.5);

    // Simulace ikony pomocí základních tvarů
    _drawIconShape(canvas, center, iconPaint);
  }

  void _drawIconShape(Canvas canvas, Offset center, Paint paint) {
    final iconRadius = iconSize / 2;
    
    switch (icon.codePoint) {
      case 0xe0b7: // Icons.book (deník)
        _drawBookIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0c8: // Icons.place (místa)
        _drawPlaceIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0ca: // Icons.event (události)
        _drawEventIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0d0: // Icons.person (profil)
        _drawPersonIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0e8: // Icons.location_city (průvodce)
        _drawCityIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0e2: // Icons.search (vyhledávání)
        _drawSearchIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0d1: // Icons.restaurant (restaurace)
        _drawRestaurantIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0b4: // Icons.beach_access (pláže)
        _drawBeachIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0b9: // Icons.directions_car (doprava)
        _drawCarIcon(canvas, center, iconRadius, paint);
        break;
      case 0xe0d2: // Icons.wb_sunny (počasí)
        _drawSunIcon(canvas, center, iconRadius, paint);
        break;
      default:
        _drawDefaultIcon(canvas, center, iconRadius, paint);
    }
  }

  void _drawBookIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    final rect = Rect.fromCenter(
      center: center,
      width: radius * 1.4,
      height: radius * 1.6,
    );
    
    // Obálka knihy
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(2)),
      paint,
    );
    
    // Stránky
    final pagesPaint = Paint()
      ..color = paint.color.withValues(alpha: 0.7)
      ..style = PaintingStyle.fill;
    
    final pagesRect = Rect.fromCenter(
      center: Offset(center.dx + 2, center.dy),
      width: radius * 1.2,
      height: radius * 1.4,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(pagesRect, const Radius.circular(1)),
      pagesPaint,
    );
  }

  void _drawPlaceIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Špička pin
    final path = Path();
    path.moveTo(center.dx, center.dy - radius);
    path.lineTo(center.dx - radius * 0.6, center.dy + radius * 0.2);
    path.lineTo(center.dx + radius * 0.6, center.dy + radius * 0.2);
    path.close();
    
    canvas.drawPath(path, paint);
    
    // Kruh nahoře
    canvas.drawCircle(
      Offset(center.dx, center.dy - radius * 0.3),
      radius * 0.4,
      paint,
    );
  }

  void _drawEventIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Kalendář pozadí
    final rect = Rect.fromCenter(
      center: center,
      width: radius * 1.4,
      height: radius * 1.6,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(3)),
      paint,
    );
    
    // Horní část kalendáře
    final headerPaint = Paint()
      ..color = paint.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;
    
    final headerRect = Rect.fromLTWH(
      rect.left,
      rect.top,
      rect.width,
      rect.height * 0.3,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(headerRect, const Radius.circular(3)),
      headerPaint,
    );
  }

  void _drawPersonIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Hlava
    canvas.drawCircle(
      Offset(center.dx, center.dy - radius * 0.3),
      radius * 0.4,
      paint,
    );
    
    // Tělo
    final bodyPath = Path();
    bodyPath.moveTo(center.dx - radius * 0.6, center.dy + radius);
    bodyPath.lineTo(center.dx - radius * 0.3, center.dy + radius * 0.1);
    bodyPath.lineTo(center.dx + radius * 0.3, center.dy + radius * 0.1);
    bodyPath.lineTo(center.dx + radius * 0.6, center.dy + radius);
    bodyPath.close();
    
    canvas.drawPath(bodyPath, paint);
  }

  void _drawCityIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Budovy různých výšek
    final buildings = [
      Rect.fromLTWH(center.dx - radius * 0.8, center.dy - radius * 0.2, radius * 0.4, radius * 1.2),
      Rect.fromLTWH(center.dx - radius * 0.3, center.dy - radius * 0.8, radius * 0.4, radius * 1.8),
      Rect.fromLTWH(center.dx + radius * 0.2, center.dy - radius * 0.5, radius * 0.4, radius * 1.5),
    ];
    
    for (final building in buildings) {
      canvas.drawRRect(
        RRect.fromRectAndRadius(building, const Radius.circular(1)),
        paint,
      );
    }
  }

  void _drawSearchIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Kruh lupy
    final circlePaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.15;
    
    canvas.drawCircle(
      Offset(center.dx - radius * 0.2, center.dy - radius * 0.2),
      radius * 0.5,
      circlePaint,
    );
    
    // Rukojeť
    final handlePaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.15
      ..strokeCap = StrokeCap.round;
    
    canvas.drawLine(
      Offset(center.dx + radius * 0.2, center.dy + radius * 0.2),
      Offset(center.dx + radius * 0.6, center.dy + radius * 0.6),
      handlePaint,
    );
  }

  void _drawRestaurantIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Vidlička vlevo
    final forkPaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.1
      ..strokeCap = StrokeCap.round;
    
    // Rukojeť vidličky
    canvas.drawLine(
      Offset(center.dx - radius * 0.3, center.dy - radius * 0.8),
      Offset(center.dx - radius * 0.3, center.dy + radius * 0.8),
      forkPaint,
    );
    
    // Zuby vidličky
    for (int i = 0; i < 3; i++) {
      canvas.drawLine(
        Offset(center.dx - radius * 0.5 + i * radius * 0.2, center.dy - radius * 0.8),
        Offset(center.dx - radius * 0.5 + i * radius * 0.2, center.dy - radius * 0.4),
        forkPaint,
      );
    }
    
    // Nůž vpravo
    canvas.drawLine(
      Offset(center.dx + radius * 0.3, center.dy - radius * 0.8),
      Offset(center.dx + radius * 0.3, center.dy + radius * 0.8),
      forkPaint,
    );
  }

  void _drawBeachIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Slunečník
    final umbrellaPaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.fill;
    
    // Vrchní část slunečníku
    final umbrellaPath = Path();
    umbrellaPath.moveTo(center.dx - radius * 0.8, center.dy - radius * 0.2);
    umbrellaPath.quadraticBezierTo(
      center.dx, center.dy - radius * 0.8,
      center.dx + radius * 0.8, center.dy - radius * 0.2,
    );
    umbrellaPath.lineTo(center.dx, center.dy - radius * 0.2);
    umbrellaPath.close();
    
    canvas.drawPath(umbrellaPath, umbrellaPaint);
    
    // Tyč
    final polePaint = Paint()
      ..color = paint.color.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.1;
    
    canvas.drawLine(
      Offset(center.dx, center.dy - radius * 0.2),
      Offset(center.dx, center.dy + radius * 0.8),
      polePaint,
    );
  }

  void _drawCarIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Tělo auta
    final carRect = Rect.fromCenter(
      center: center,
      width: radius * 1.6,
      height: radius * 0.8,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(carRect, Radius.circular(radius * 0.1)),
      paint,
    );
    
    // Kola
    final wheelPaint = Paint()
      ..color = paint.color.withValues(alpha: 0.7)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(center.dx - radius * 0.5, center.dy + radius * 0.5),
      radius * 0.2,
      wheelPaint,
    );
    
    canvas.drawCircle(
      Offset(center.dx + radius * 0.5, center.dy + radius * 0.5),
      radius * 0.2,
      wheelPaint,
    );
  }

  void _drawSunIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Střed slunce
    canvas.drawCircle(center, radius * 0.4, paint);
    
    // Paprsky
    final rayPaint = Paint()
      ..color = paint.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = radius * 0.08
      ..strokeCap = StrokeCap.round;
    
    for (int i = 0; i < 8; i++) {
      final angle = (i * 45) * math.pi / 180;
      final start = Offset(
        center.dx + radius * 0.6 * math.cos(angle),
        center.dy + radius * 0.6 * math.sin(angle),
      );
      final end = Offset(
        center.dx + radius * 0.9 * math.cos(angle),
        center.dy + radius * 0.9 * math.sin(angle),
      );
      
      canvas.drawLine(start, end, rayPaint);
    }
  }

  void _drawDefaultIcon(Canvas canvas, Offset center, double radius, Paint paint) {
    // Výchozí kruh
    canvas.drawCircle(center, radius * 0.6, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
