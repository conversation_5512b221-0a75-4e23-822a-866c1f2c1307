import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/gamification_onboarding.dart';
// import '../services/achievement_system_service.dart';

/// 🎮 GAMIFICATION ONBOARDING SERVICE - Maximize retention impact
class GamificationOnboardingService {
  static final GamificationOnboardingService _instance =
      GamificationOnboardingService._internal();
  factory GamificationOnboardingService() => _instance;
  GamificationOnboardingService._internal();

  bool _isInitialized = false;
  final List<OnboardingQuest> _quests = [];
  final List<OnboardingMilestone> _milestones = [];
  final Map<String, UserOnboardingProgress> _userProgress = {};
  final StreamController<OnboardingEvent> _eventController =
      StreamController.broadcast();

  /// Stream onboarding událostí
  Stream<OnboardingEvent> get onboardingEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🎮 Inicializuji Gamification Onboarding Service...');

      await _loadOnboardingQuests();
      await _loadOnboardingMilestones();
      await _loadUserProgress();

      _isInitialized = true;
      debugPrint('✅ Gamification Onboarding Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Gamification Onboarding: $e');
      await _createDefaultQuests();
      _isInitialized = true;
    }
  }

  /// Spuštění gamifikovaného onboardingu
  Future<OnboardingJourney> startGamifiedOnboarding({
    required String userId,
    OnboardingPersonality? personality,
  }) async {
    try {
      // Analýza personality pro personalizaci
      final detectedPersonality =
          personality ?? await _detectOnboardingPersonality(userId);

      // Výběr questů podle personality
      final selectedQuests = _selectQuestsForPersonality(detectedPersonality);

      // Vytvoření onboarding journey
      final journey = OnboardingJourney(
        id: 'journey_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Onboarding Journey',
        description: 'Personalizovaná cesta onboardingu',
        quests: selectedQuests,
        milestones: _milestones,
        estimatedDays: 7,
        targetPersonality: detectedPersonality,
        configuration: {
          'userId': userId,
          'startedAt': DateTime.now().toIso8601String(),
        },
      );

      // Uložení pokroku
      _userProgress[userId] = UserOnboardingProgress(
        userId: userId,
        journeyId: journey.id,
        completedQuests: [],
        completedMilestones: [],
        totalPoints: 0,
        currentStreak: 0,
        startedAt: DateTime.now(),
        personality: detectedPersonality,
        preferences: {},
        categoryProgress: {},
      );

      await _saveUserProgress();

      // První quest
      await _startQuest(userId, selectedQuests.first);

      _eventController.add(
        OnboardingEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: OnboardingEventType.questStarted,
          userId: userId,
          timestamp: DateTime.now(),
          data: {
            'personality': detectedPersonality.name,
            'questsCount': selectedQuests.length,
          },
        ),
      );

      debugPrint(
        '🎮 Gamifikovaný onboarding spuštěn: $userId (${detectedPersonality.name})',
      );
      return journey;
    } catch (e) {
      debugPrint('❌ Chyba při spuštění onboardingu: $e');
      rethrow;
    }
  }

  /// Dokončení questu
  Future<QuestCompletionResult> completeQuest({
    required String userId,
    required String questId,
    Map<String, dynamic>? completionData,
  }) async {
    try {
      final progress = _userProgress[userId];
      if (progress == null) {
        throw Exception('Uživatel nemá aktivní onboarding');
      }

      final quest = _quests.firstWhere((q) => q.id == questId);

      // Výpočet XP a odměn
      final baseXP = quest.xpReward;
      final bonusXP = _calculateBonusXP(quest, completionData);
      final totalXP = baseXP + bonusXP;

      // Aktualizace pokroku
      final updatedProgress = progress.copyWith(
        completedQuests: [...progress.completedQuests, questId],
        totalXP: progress.totalXP + totalXP,
        level: _calculateLevel(progress.totalXP + totalXP),
      );

      _userProgress[userId] = updatedProgress;
      await _saveUserProgress();

      // Kontrola milníků
      final unlockedMilestones = await _checkMilestones(
        userId,
        updatedProgress,
      );

      // Kontrola achievementů
      // TODO: Implementovat achievement system
      debugPrint('🏆 Achievement check: quest completed $questId');

      // Další quest
      OnboardingQuest? nextQuest;
      final journey = await _getOnboardingJourney(userId);
      if (journey != null) {
        final currentIndex = journey.quests.indexWhere((q) => q.id == questId);
        if (currentIndex >= 0 && currentIndex < journey.quests.length - 1) {
          nextQuest = journey.quests[currentIndex + 1];
          await _startQuest(userId, nextQuest);
        } else {
          // Onboarding dokončen
          await _completeOnboarding(userId);
        }
      }

      final result = QuestCompletionResult(
        quest: quest,
        xpEarned: totalXP,
        bonusXP: bonusXP,
        newLevel: updatedProgress.level,
        leveledUp: updatedProgress.level > progress.level,
        unlockedMilestones: unlockedMilestones,
        nextQuest: nextQuest,
        completedAt: DateTime.now(),
      );

      _eventController.add(
        OnboardingEvent(
          type: OnboardingEventType.questCompleted,
          userId: userId,
          timestamp: DateTime.now(),
          data: {
            'questId': questId,
            'xpEarned': totalXP,
            'newLevel': updatedProgress.level,
          },
        ),
      );

      debugPrint('✅ Quest dokončen: $questId (+${totalXP} XP)');
      return result;
    } catch (e) {
      debugPrint('❌ Chyba při dokončování questu: $e');
      rethrow;
    }
  }

  /// Spuštění konkrétního questu
  Future<void> _startQuest(String userId, OnboardingQuest quest) async {
    try {
      // Aktualizace current quest
      final progress = _userProgress[userId];
      if (progress != null) {
        _userProgress[userId] = progress.copyWith(currentQuest: quest.id);
        await _saveUserProgress();
      }

      _eventController.add(
        OnboardingEvent(
          type: OnboardingEventType.questStarted,
          userId: userId,
          timestamp: DateTime.now(),
          data: {
            'questId': quest.id,
            'questType': quest.type.name,
            'difficulty': quest.difficulty.name,
          },
        ),
      );

      debugPrint('🎯 Quest spuštěn: ${quest.title}');
    } catch (e) {
      debugPrint('❌ Chyba při spuštění questu: $e');
    }
  }

  /// Dokončení celého onboardingu
  Future<OnboardingCompletionReward> _completeOnboarding(String userId) async {
    try {
      final progress = _userProgress[userId];
      if (progress == null) return OnboardingCompletionReward.empty();

      // Completion bonus
      const completionBonus = 500;
      final finalXP = progress.totalXP + completionBonus;
      final finalLevel = _calculateLevel(finalXP);

      // Speciální achievement
      await AchievementSystemService().checkAchievements(
        trigger: AchievementTrigger.challengeComplete,
        data: {'onboardingCompleted': true},
      );

      // Completion reward
      final reward = OnboardingCompletionReward(
        totalXP: finalXP,
        finalLevel: finalLevel,
        completionBonus: completionBonus,
        unlockedFeatures: [
          'Advanced Search Preview',
          'Analytics Dashboard Preview',
          'Premium Trial Access',
        ],
        specialAchievement: 'Onboarding Master',
        premiumTrialDays: 7,
        completedAt: DateTime.now(),
      );

      // Aktualizace pokroku
      _userProgress[userId] = progress.copyWith(
        totalXP: finalXP,
        level: finalLevel,
        isCompleted: true,
        completedAt: DateTime.now(),
      );
      await _saveUserProgress();

      _eventController.add(
        OnboardingEvent(
          type: OnboardingEventType.onboardingCompleted,
          userId: userId,
          timestamp: DateTime.now(),
          data: {
            'finalXP': finalXP,
            'finalLevel': finalLevel,
            'completionBonus': completionBonus,
          },
        ),
      );

      debugPrint('🎉 Onboarding dokončen: $userId (Level $finalLevel)');
      return reward;
    } catch (e) {
      debugPrint('❌ Chyba při dokončování onboardingu: $e');
      return OnboardingCompletionReward.empty();
    }
  }

  /// Detekce onboarding personality
  Future<OnboardingPersonality> _detectOnboardingPersonality(
    String userId,
  ) async {
    // Simulace detekce personality na základě prvních interakcí
    await Future.delayed(const Duration(milliseconds: 500));

    final personalities = OnboardingPersonality.values;
    return personalities[Random().nextInt(personalities.length)];
  }

  /// Výběr questů podle personality
  List<OnboardingQuest> _selectQuestsForPersonality(
    OnboardingPersonality personality,
  ) {
    final allQuests = List<OnboardingQuest>.from(_quests);

    switch (personality) {
      case OnboardingPersonality.achiever:
        // Achievers milují výzvy a pokrok
        return allQuests
            .where(
              (q) =>
                  q.type == QuestType.achievement ||
                  q.difficulty == QuestDifficulty.hard,
            )
            .take(5)
            .toList();

      case OnboardingPersonality.explorer:
        // Explorers chtějí objevovat funkce
        return allQuests
            .where(
              (q) =>
                  q.type == QuestType.discovery || q.type == QuestType.feature,
            )
            .take(5)
            .toList();

      case OnboardingPersonality.socializer:
        // Socializers se zaměřují na sdílení
        return allQuests
            .where(
              (q) =>
                  q.type == QuestType.social ||
                  q.category == QuestCategory.sharing,
            )
            .take(5)
            .toList();

      case OnboardingPersonality.casual:
        // Casual users chtějí jednoduché úkoly
        return allQuests
            .where((q) => q.difficulty == QuestDifficulty.easy)
            .take(4)
            .toList();
    }
  }

  /// Kontrola milníků
  Future<List<OnboardingMilestone>> _checkMilestones(
    String userId,
    UserOnboardingProgress progress,
  ) async {
    final unlockedMilestones = <OnboardingMilestone>[];

    for (final milestone in _milestones) {
      if (progress.achievements.contains(milestone.id)) continue;

      bool isUnlocked = false;

      switch (milestone.type) {
        case MilestoneType.questCount:
          isUnlocked = progress.completedQuests.length >= milestone.requirement;
          break;
        case MilestoneType.xpTotal:
          isUnlocked = progress.totalXP >= milestone.requirement;
          break;
        case MilestoneType.level:
          isUnlocked = progress.level >= milestone.requirement;
          break;
        case MilestoneType.timeSpent:
          final timeSpent = DateTime.now().difference(progress.startedAt);
          isUnlocked = timeSpent.inMinutes >= milestone.requirement;
          break;
      }

      if (isUnlocked) {
        unlockedMilestones.add(milestone);

        // Aktualizace pokroku
        _userProgress[userId] = progress.copyWith(
          achievements: [...progress.achievements, milestone.id],
        );
      }
    }

    if (unlockedMilestones.isNotEmpty) {
      await _saveUserProgress();
    }

    return unlockedMilestones;
  }

  /// Výpočet bonus XP
  int _calculateBonusXP(OnboardingQuest quest, Map<String, dynamic>? data) {
    int bonus = 0;

    // Speed bonus
    if (data?['completionTime'] != null) {
      final completionTime = data!['completionTime'] as Duration;
      if (completionTime < quest.estimatedDuration * 0.5) {
        bonus += 50; // Speed bonus
      }
    }

    // Quality bonus
    if (data?['quality'] != null) {
      final quality = data!['quality'] as double;
      if (quality > 0.8) {
        bonus += 30; // Quality bonus
      }
    }

    // First try bonus
    if (data?['attempts'] == 1) {
      bonus += 20; // First try bonus
    }

    return bonus;
  }

  /// Výpočet levelu z XP
  int _calculateLevel(int xp) {
    // Exponenciální růst: level = sqrt(xp / 100) + 1
    return (sqrt(xp / 100)).floor() + 1;
  }

  /// Získání onboarding journey
  Future<OnboardingJourney?> _getOnboardingJourney(String userId) async {
    final progress = _userProgress[userId];
    if (progress == null) return null;

    // Rekonstrukce journey z pokroku
    // V produkci by se načítalo z databáze
    return null;
  }

  /// Načítání a ukládání dat
  Future<void> _loadOnboardingQuests() async {
    await _createDefaultQuests();
  }

  Future<void> _loadOnboardingMilestones() async {
    await _createDefaultMilestones();
  }

  Future<void> _loadUserProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString('onboarding_progress');

      if (progressJson != null) {
        final Map<String, dynamic> data = jsonDecode(progressJson);
        _userProgress.clear();
        data.forEach((userId, progressData) {
          _userProgress[userId] = UserOnboardingProgress.fromJson(progressData);
        });
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání onboarding pokroku: $e');
    }
  }

  Future<void> _saveUserProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = _userProgress.map(
        (userId, progress) => MapEntry(userId, progress.toJson()),
      );
      await prefs.setString('onboarding_progress', jsonEncode(data));
    } catch (e) {
      debugPrint('❌ Chyba při ukládání onboarding pokroku: $e');
    }
  }

  Future<void> _createDefaultQuests() async {
    _quests.addAll([
      // Základní questy
      OnboardingQuest(
        id: 'first_entry',
        title: 'První vzpomínka',
        description: 'Napište svůj první zápis do deníku',
        type: QuestType.basic,
        category: QuestCategory.writing,
        difficulty: QuestDifficulty.easy,
        xpReward: 100,
        estimatedDuration: const Duration(minutes: 5),
        instructions: [
          'Klikněte na tlačítko "Nový zápis"',
          'Napište alespoň 50 slov',
          'Přidejte náladu',
          'Uložte zápis',
        ],
        hints: ['Začněte popisem dnešního dne', 'Nezapomeňte na detaily'],
      ),

      OnboardingQuest(
        id: 'add_photo',
        title: 'Zachyťte moment',
        description: 'Přidejte fotografii k vašemu zápisu',
        type: QuestType.feature,
        category: QuestCategory.media,
        difficulty: QuestDifficulty.easy,
        xpReward: 75,
        estimatedDuration: const Duration(minutes: 3),
        instructions: [
          'Otevřete váš zápis',
          'Klikněte na ikonu fotoaparátu',
          'Vyberte nebo pořiďte fotografii',
          'Uložte změny',
        ],
        hints: ['Fotografie oživí vaše vzpomínky'],
      ),

      OnboardingQuest(
        id: 'explore_search',
        title: 'Objevte vyhledávání',
        description: 'Vyzkoušejte pokročilé vyhledávání',
        type: QuestType.discovery,
        category: QuestCategory.features,
        difficulty: QuestDifficulty.medium,
        xpReward: 150,
        estimatedDuration: const Duration(minutes: 4),
        instructions: [
          'Přejděte na vyhledávání',
          'Vyzkoušejte filtr podle nálady',
          'Vyhledejte podle data',
          'Prohlédněte si výsledky',
        ],
        hints: ['Můžete kombinovat více filtrů'],
      ),

      OnboardingQuest(
        id: 'achievement_unlock',
        title: 'První úspěch',
        description: 'Odemkněte svůj první achievement',
        type: QuestType.achievement,
        category: QuestCategory.gamification,
        difficulty: QuestDifficulty.medium,
        xpReward: 200,
        estimatedDuration: const Duration(minutes: 2),
        instructions: [
          'Pokračujte v psaní',
          'Sledujte pokrok v achievementech',
          'Oslavte svůj úspěch!',
        ],
        hints: ['Každý achievement má svou hodnotu'],
      ),

      OnboardingQuest(
        id: 'share_memory',
        title: 'Sdílejte radost',
        description: 'Sdílejte vzpomínku s přáteli',
        type: QuestType.social,
        category: QuestCategory.sharing,
        difficulty: QuestDifficulty.medium,
        xpReward: 125,
        estimatedDuration: const Duration(minutes: 3),
        instructions: [
          'Vyberte vzpomínku ke sdílení',
          'Klikněte na tlačítko sdílení',
          'Vyberte způsob sdílení',
          'Potvrďte akci',
        ],
        hints: ['Sdílené vzpomínky spojují lidi'],
      ),
    ]);
  }

  Future<void> _createDefaultMilestones() async {
    _milestones.addAll([
      OnboardingMilestone(
        id: 'first_steps',
        title: 'První kroky',
        description: 'Dokončili jste první quest',
        type: MilestoneType.questCount,
        requirement: 1,
        reward: 'Odemčení základních funkcí',
        icon: '👶',
      ),

      OnboardingMilestone(
        id: 'getting_started',
        title: 'Začínáme',
        description: 'Dokončili jste 3 questy',
        type: MilestoneType.questCount,
        requirement: 3,
        reward: 'Přístup k pokročilým funkcím',
        icon: '🚀',
      ),

      OnboardingMilestone(
        id: 'xp_collector',
        title: 'Sběratel XP',
        description: 'Získali jste 500 XP',
        type: MilestoneType.xpTotal,
        requirement: 500,
        reward: 'Speciální badge',
        icon: '⭐',
      ),

      OnboardingMilestone(
        id: 'level_up',
        title: 'Levelování',
        description: 'Dosáhli jste 3. levelu',
        type: MilestoneType.level,
        requirement: 3,
        reward: 'Premium preview',
        icon: '🏆',
      ),
    ]);
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<OnboardingQuest> get quests => List.unmodifiable(_quests);
  List<OnboardingMilestone> get milestones => List.unmodifiable(_milestones);
  UserOnboardingProgress? getUserProgress(String userId) =>
      _userProgress[userId];
}
