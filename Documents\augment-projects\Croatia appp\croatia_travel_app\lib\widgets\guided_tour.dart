import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Guided tour pro představení hlavních funkcí aplikace
class GuidedTour extends StatefulWidget {
  final Widget child;
  final List<TourStep> steps;
  final VoidCallback? onComplete;

  const GuidedTour({
    super.key,
    required this.child,
    required this.steps,
    this.onComplete,
  });

  @override
  State<GuidedTour> createState() => _GuidedTourState();
}

class _GuidedTourState extends State<GuidedTour>
    with TickerProviderStateMixin {
  int _currentStep = 0;
  bool _isVisible = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    // Spustit tour po krátké pauze
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _startTour();
        }
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startTour() {
    setState(() {
      _isVisible = true;
      _currentStep = 0;
    });
    _animationController.forward();
  }

  void _nextStep() {
    if (_currentStep < widget.steps.length - 1) {
      _animationController.reverse().then((_) {
        setState(() {
          _currentStep++;
        });
        _animationController.forward();
      });
    } else {
      _completeTour();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _animationController.reverse().then((_) {
        setState(() {
          _currentStep--;
        });
        _animationController.forward();
      });
    }
  }

  void _completeTour() {
    _animationController.reverse().then((_) {
      setState(() {
        _isVisible = false;
      });
      widget.onComplete?.call();
    });
  }

  void _skipTour() {
    _completeTour();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_isVisible) _buildTourOverlay(),
      ],
    );
  }

  Widget _buildTourOverlay() {
    final step = widget.steps[_currentStep];
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Stack(
            children: [
              // Tmavé pozadí s výřezem
              _buildBackgroundOverlay(step),
              
              // Tooltip s informacemi
              _buildTooltip(step),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBackgroundOverlay(TourStep step) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withValues(alpha: 0.7),
      child: CustomPaint(
        painter: HolePainter(
          holeRect: step.targetRect,
          holeRadius: step.holeRadius,
        ),
      ),
    );
  }

  Widget _buildTooltip(TourStep step) {
    return Positioned(
      left: step.tooltipPosition.dx,
      top: step.tooltipPosition.dy,
      child: Transform.scale(
        scale: _scaleAnimation.value,
        child: Container(
          width: 280,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Titulek
              Text(
                step.title,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF006994),
                ),
              ),

              const SizedBox(height: 12),

              // Popis
              Text(
                step.description,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: const Color(0xFF2C2C2C),
                  height: 1.5,
                ),
              ),

              const SizedBox(height: 20),

              // Navigační tlačítka
              Row(
                children: [
                  // Skip tlačítko
                  TextButton(
                    onPressed: _skipTour,
                    child: Text(
                      'Přeskočit',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Indikátor kroků
                  Text(
                    '${_currentStep + 1}/${widget.steps.length}',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: const Color(0xFF666666),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Zpět tlačítko
                  if (_currentStep > 0)
                    TextButton(
                      onPressed: _previousStep,
                      child: Text(
                        'Zpět',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: const Color(0xFF006994),
                        ),
                      ),
                    ),

                  // Další/Dokončit tlačítko
                  ElevatedButton(
                    onPressed: _nextStep,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF006994),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      _currentStep < widget.steps.length - 1 ? 'Další' : 'Hotovo',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Model pro krok v guided tour
class TourStep {
  final String title;
  final String description;
  final Rect targetRect;
  final Offset tooltipPosition;
  final double holeRadius;

  TourStep({
    required this.title,
    required this.description,
    required this.targetRect,
    required this.tooltipPosition,
    this.holeRadius = 8.0,
  });
}

/// Custom painter pro vytvoření výřezu v overlay
class HolePainter extends CustomPainter {
  final Rect holeRect;
  final double holeRadius;

  HolePainter({
    required this.holeRect,
    required this.holeRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.transparent
      ..blendMode = BlendMode.clear;

    // Vytvořit výřez
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        holeRect,
        Radius.circular(holeRadius),
      ),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Helper pro vytvoření tour kroků pro hlavní navigaci
class MainNavigationTourSteps {
  static List<TourStep> createSteps(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return [
      TourStep(
        title: 'Váš cestovní deník',
        description: 'Zde můžete psát a prohlížet své cestovní zážitky. Přidávejte fotografie a vytvářejte krásné vzpomínky.',
        targetRect: Rect.fromLTWH(0, screenSize.height - 80, screenSize.width / 5, 80),
        tooltipPosition: Offset(20, screenSize.height - 200),
      ),
      TourStep(
        title: 'Průvodce Chorvatskem',
        description: 'Objevte praktické informace o životě v Chorvatsku, dopravě a místních službách.',
        targetRect: Rect.fromLTWH(screenSize.width / 5, screenSize.height - 80, screenSize.width / 5, 80),
        tooltipPosition: Offset(20, screenSize.height - 200),
      ),
      TourStep(
        title: 'Místa k návštěvě',
        description: 'Procházejte tisíce úžasných míst v Chorvatsku. Filtrujte podle regionů a typů.',
        targetRect: Rect.fromLTWH(screenSize.width * 2 / 5, screenSize.height - 80, screenSize.width / 5, 80),
        tooltipPosition: Offset(20, screenSize.height - 200),
      ),
      TourStep(
        title: 'Události a aktivity',
        description: 'Najděte zajímavé události, festivaly a aktivity ve vašem okolí.',
        targetRect: Rect.fromLTWH(screenSize.width * 3 / 5, screenSize.height - 80, screenSize.width / 5, 80),
        tooltipPosition: Offset(20, screenSize.height - 200),
      ),
      TourStep(
        title: 'Váš profil',
        description: 'Spravujte své nastavení, prohlížejte statistiky a přizpůsobte si aplikaci podle svých potřeb.',
        targetRect: Rect.fromLTWH(screenSize.width * 4 / 5, screenSize.height - 80, screenSize.width / 5, 80),
        tooltipPosition: Offset(20, screenSize.height - 280),
      ),
      TourStep(
        title: 'Vyhledávání',
        description: 'Použijte globální vyhledávání pro rychlé nalezení míst, událostí nebo služeb.',
        targetRect: Rect.fromLTWH(screenSize.width - 120, 0, 60, 80),
        tooltipPosition: Offset(screenSize.width - 300, 100),
      ),
    ];
  }
}
