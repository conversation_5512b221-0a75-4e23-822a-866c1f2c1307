import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'theme/watercolor_theme.dart';
import 'widgets/watercolor_background.dart';
import 'widgets/watercolor_icon.dart';
import 'views/weather_screen.dart';
import 'views/currency_converter_screen.dart';
import 'views/places_screen.dart';
import 'views/events_screen.dart';
import 'views/cuisine_screen.dart';
import 'views/diary_screen.dart';
import 'views/dictionary_screen.dart';
import 'views/settings_screen.dart';
import 'views/croatian_life_screen.dart';
import 'views/smart_city_screen.dart';
import 'views/restaurant_discovery_screen.dart';
import 'views/beach_discovery_screen.dart';
import 'views/accommodation_discovery_screen.dart';
import 'views/cultural_discovery_screen.dart';
import 'views/transportation_hub_screen.dart';
import 'views/entertainment_activities_screen.dart';
import 'views/emergency_services_screen.dart';
import 'views/camera_screen.dart';
import 'views/personalization_screen.dart';
import 'views/advanced_settings_screen.dart';
import 'views/ai_assistant_screen.dart';
import 'views/ticket_screen.dart';
import 'views/interactive_map_screen.dart';
import 'views/offline_manager_screen.dart';
import 'views/wallet_screen.dart';
import 'views/community_screen.dart';
import 'views/croatia_idos_screen.dart';
import 'views/contacts_directory_screen.dart';

void main() {
  runApp(const CroatiaTravelApp());
}

class CroatiaTravelApp extends StatelessWidget {
  const CroatiaTravelApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Adriatic Diary - Croatia Travel App',
      theme: WatercolorTheme.watercolorThemeData,
      home: const MainNavigationScreen(),
    );
  }
}

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = const [
    DiaryScreen(), // 📖 DENÍK jako hlavní stránka
    CroatianLifeScreen(), // 🗺️ ŽIVOT V CHORVATSKU
    PlacesScreen(), // 📍 MÍSTA A PAMÁTKY
    EventsScreen(), // 📅 UDÁLOSTI A AKTIVITY
    SettingsScreen(), // ⚙️ NASTAVENÍ A PROFIL
  ];

  final List<BottomNavigationBarItem> _navItems = [
    BottomNavigationBarItem(
      icon: WatercolorIcon(
        icon: Icons.book,
        size: 24,
        color: WatercolorTheme.adriaticBlue,
        semanticLabel: 'Deník',
      ),
      label: 'Deník',
    ),
    BottomNavigationBarItem(
      icon: WatercolorIcon(
        icon: Icons.location_city,
        size: 24,
        color: WatercolorTheme.adriaticBlue,
        semanticLabel: 'Průvodce',
      ),
      label: 'Průvodce',
    ),
    BottomNavigationBarItem(
      icon: WatercolorIcon(
        icon: Icons.place,
        size: 24,
        color: WatercolorTheme.adriaticBlue,
        semanticLabel: 'Místa',
      ),
      label: 'Místa',
    ),
    BottomNavigationBarItem(
      icon: WatercolorIcon(
        icon: Icons.event,
        size: 24,
        color: WatercolorTheme.adriaticBlue,
        semanticLabel: 'Události',
      ),
      label: 'Události',
    ),
    BottomNavigationBarItem(
      icon: WatercolorIcon(
        icon: Icons.person,
        size: 24,
        color: WatercolorTheme.adriaticBlue,
        semanticLabel: 'Profil',
      ),
      label: 'Profil',
    ),
  ];

  void _showAllFeaturesScreen() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(0xFFF8F6F0),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Text(
                    'Všechny funkce',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            // Grid všech funkcí
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                padding: const EdgeInsets.all(16),
                children: [
                  _buildFeatureCard('Kuchyně', Icons.restaurant, 'cuisine'),
                  _buildFeatureCard('Slovník', Icons.translate, 'dictionary'),
                  _buildFeatureCard(
                    'Kultura',
                    Icons.account_balance,
                    'culture',
                  ),
                  _buildFeatureCard(
                    'Zábava',
                    Icons.attractions,
                    'entertainment',
                  ),
                  _buildFeatureCard(
                    'Smart City',
                    Icons.location_city,
                    'smart_city',
                  ),
                  _buildFeatureCard(
                    'Vstupenky',
                    Icons.confirmation_number,
                    'tickets',
                  ),
                  _buildFeatureCard(
                    'Offline',
                    Icons.offline_bolt,
                    'offline_manager',
                  ),
                  _buildFeatureCard('Kamera', Icons.camera_alt, 'camera'),
                  _buildFeatureCard(
                    'Peněženka',
                    Icons.account_balance_wallet,
                    'wallet',
                  ),
                  _buildFeatureCard('Komunita', Icons.people, 'community'),
                  _buildFeatureCard('IDOS', Icons.train, 'croatia_idos'),
                  _buildFeatureCard(
                    'Kontakty',
                    Icons.contact_phone,
                    'contacts',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(String title, IconData icon, String value) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        _navigateToFeature(value);
      },
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: const Color(0xFF006994)),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C2C2C),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToFeature(String feature) {
    // Použije stejnou logiku jako původní switch
    switch (feature) {
      case 'cuisine':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CuisineScreen()),
        );
        break;
      case 'dictionary':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const DictionaryScreen()),
        );
        break;
      // ... další případy
    }
  }

  void _showGlobalSearch() {
    showSearch(context: context, delegate: GlobalSearchDelegate());
  }

  @override
  Widget build(BuildContext context) {
    return WatercolorBackground(
      colors: WatercolorTheme.paperGradient,
      animated: true,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: Text(
            'ADRIATIC DIARY',
            style: WatercolorTheme.headingMedium.copyWith(
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
              color: Colors.white,
            ),
          ),
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: WatercolorTheme.createWatercolorGradient(
                WatercolorTheme.adriaticGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: CustomPaint(
              painter: WatercolorNavigationPainter(),
              size: Size.infinite,
            ),
          ),
          actions: [
            // GLOBÁLNÍ VYHLEDÁVÁNÍ
            Container(
              margin: const EdgeInsets.all(4),
              decoration: WatercolorTheme.watercolorCardDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: WatercolorTheme.softRadius,
              ),
              child: IconButton(
                onPressed: _showGlobalSearch,
                icon: WatercolorIcon(
                  icon: Icons.search,
                  size: 24,
                  color: Colors.white,
                  semanticLabel: 'Vyhledat',
                ),
                tooltip: 'Vyhledat',
              ),
            ),
            // ZJEDNODUŠENÉ MENU - pouze nejdůležitější funkce
            PopupMenuButton<String>(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: WatercolorTheme.watercolorCardDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: WatercolorTheme.softRadius,
                ),
                child: WatercolorIcon(
                  icon: Icons.apps,
                  size: 24,
                  color: Colors.white,
                  semanticLabel: 'Menu',
                ),
              ),
              onSelected: (value) {
                switch (value) {
                  case 'more_features':
                    _showAllFeaturesScreen();
                    break;
                  case 'weather':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WeatherScreen(),
                      ),
                    );
                    break;
                  case 'currency':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CurrencyConverterScreen(),
                      ),
                    );
                    break;
                  case 'cuisine':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CuisineScreen(),
                      ),
                    );
                    break;
                  case 'dictionary':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DictionaryScreen(),
                      ),
                    );
                    break;
                  case 'restaurants':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const RestaurantDiscoveryScreen(),
                      ),
                    );
                    break;
                  case 'beaches':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BeachDiscoveryScreen(),
                      ),
                    );
                    break;
                  case 'accommodation':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const AccommodationDiscoveryScreen(),
                      ),
                    );
                    break;
                  case 'culture':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CulturalDiscoveryScreen(),
                      ),
                    );
                    break;
                  case 'transport':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TransportationHubScreen(),
                      ),
                    );
                    break;
                  case 'entertainment':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const EntertainmentActivitiesScreen(),
                      ),
                    );
                    break;
                  case 'smart_city':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SmartCityScreen(),
                      ),
                    );
                    break;
                  case 'emergency':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EmergencyServicesScreen(),
                      ),
                    );
                    break;
                  case 'ai_assistant':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AIAssistantScreen(),
                      ),
                    );
                    break;
                  case 'tickets':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TicketScreen(),
                      ),
                    );
                    break;
                  case 'interactive_map':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InteractiveMapScreen(),
                      ),
                    );
                    break;
                  case 'offline_manager':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const OfflineManagerScreen(),
                      ),
                    );
                    break;
                  case 'camera':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CameraScreen(),
                      ),
                    );
                    break;
                  case 'personalization':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PersonalizationScreen(),
                      ),
                    );
                    break;
                  case 'advanced_settings':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AdvancedSettingsScreen(),
                      ),
                    );
                    break;
                  case 'wallet':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WalletScreen(),
                      ),
                    );
                    break;
                  case 'community':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CommunityScreen(),
                      ),
                    );
                    break;
                  case 'croatia_idos':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CroatiaIdosScreen(),
                      ),
                    );
                    break;
                  case 'contacts':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ContactsDirectoryScreen(),
                      ),
                    );
                    break;
                }
              },
              itemBuilder: (context) => [
                // KATEGORIE 1: CESTOVNÍ NÁSTROJE
                PopupMenuItem(
                  enabled: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Text(
                      'CESTOVNÍ NÁSTROJE',
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF666666),
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                const PopupMenuItem(
                  value: 'weather',
                  child: Row(
                    children: [
                      Icon(Icons.wb_sunny, color: Color(0xFFFFB74D)),
                      SizedBox(width: 12),
                      Text('Počasí'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'currency',
                  child: Row(
                    children: [
                      Icon(Icons.currency_exchange, color: Color(0xFF4CAF50)),
                      SizedBox(width: 12),
                      Text('Měnový převodník'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'interactive_map',
                  child: Row(
                    children: [
                      Icon(Icons.map_outlined, color: Color(0xFF2196F3)),
                      SizedBox(width: 12),
                      Text('Mapa'),
                    ],
                  ),
                ),

                // KATEGORIE 2: OBJEVOVÁNÍ
                PopupMenuItem(
                  enabled: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      'OBJEVOVÁNÍ',
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF666666),
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                const PopupMenuItem(
                  value: 'restaurants',
                  child: Row(
                    children: [
                      Icon(Icons.restaurant_menu, color: Color(0xFFFF6B35)),
                      SizedBox(width: 12),
                      Text('Restaurace'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'beaches',
                  child: Row(
                    children: [
                      Icon(Icons.beach_access, color: Color(0xFF006994)),
                      SizedBox(width: 12),
                      Text('Pláže'),
                    ],
                  ),
                ),

                // KATEGORIE 3: SLUŽBY
                PopupMenuItem(
                  enabled: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      'SLUŽBY',
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF666666),
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                const PopupMenuItem(
                  value: 'transport',
                  child: Row(
                    children: [
                      Icon(Icons.directions_car, color: Color(0xFF1976D2)),
                      SizedBox(width: 12),
                      Text('Doprava'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'emergency',
                  child: Row(
                    children: [
                      Icon(Icons.emergency, color: Color(0xFFDC143C)),
                      SizedBox(width: 12),
                      Text('Nouzové služby'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'ai_assistant',
                  child: Row(
                    children: [
                      Icon(Icons.smart_toy, color: Color(0xFF9C27B0)),
                      SizedBox(width: 12),
                      Text('AI Asistent'),
                    ],
                  ),
                ),

                // KATEGORIE 4: VÍCE FUNKCÍ
                PopupMenuItem(
                  enabled: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      'VÍCE FUNKCÍ',
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF666666),
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                PopupMenuItem(
                  value: 'more_features',
                  child: Row(
                    children: [
                      Icon(Icons.apps, color: Color(0xFF006994)),
                      SizedBox(width: 12),
                      Text('Všechny funkce'),
                      Spacer(),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Color(0xFF666666),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: _screens[_currentIndex],
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            gradient: WatercolorTheme.createWatercolorGradient(
              [
                WatercolorTheme.adriaticBlue.withValues(alpha: 0.1),
                WatercolorTheme.mediterraneanTeal.withValues(alpha: 0.05),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: CustomPaint(
            painter: WatercolorBottomNavPainter(),
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.transparent,
              elevation: 0,
              selectedItemColor: WatercolorTheme.adriaticBlue,
              unselectedItemColor: WatercolorTheme.charcoal.withValues(
                alpha: 0.6,
              ),
              selectedLabelStyle: WatercolorTheme.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: WatercolorTheme.labelMedium.copyWith(
                fontWeight: FontWeight.w400,
              ),
              items: _navItems,
            ),
          ),
        ),
      ),
    );
  }
}

// Watercolor painter pro hlavní navigaci
class WatercolorNavigationPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Jemné watercolor vlny v navigaci
    final path1 = Path();
    path1.moveTo(0, size.height * 0.4);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.2,
      size.width * 0.5,
      size.height * 0.5,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.8,
      size.width,
      size.height * 0.3,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.3,
      size.width * 0.7,
      size.height * 0.7,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.1);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Watercolor painter pro bottom navigation
class WatercolorBottomNavPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemné watercolor vlny v bottom navigation
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, 0);
    path1.lineTo(0, 0);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.1);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, 0);
    path2.lineTo(0, 0);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Globální vyhledávání
class GlobalSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'Vyhledat místa, události, služby...';

  @override
  ThemeData appBarTheme(BuildContext context) {
    return Theme.of(context).copyWith(
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF006994),
        foregroundColor: Colors.white,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        hintStyle: TextStyle(color: Colors.white70),
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(onPressed: () => query = '', icon: const Icon(Icons.clear)),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      onPressed: () => close(context, ''),
      icon: const Icon(Icons.arrow_back),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Začněte psát pro vyhledávání',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    // Simulace výsledků vyhledávání
    final results =
        [
              'Dubrovník - Staré město',
              'Plitvická jezera',
              'Zlatni Rat - Bol',
              'Rovinj',
              'Krka National Park',
            ]
            .where((item) => item.toLowerCase().contains(query.toLowerCase()))
            .toList();

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        return ListTile(
          leading: const Icon(Icons.place, color: Color(0xFF006994)),
          title: Text(results[index]),
          onTap: () => close(context, results[index]),
        );
      },
    );
  }
}
