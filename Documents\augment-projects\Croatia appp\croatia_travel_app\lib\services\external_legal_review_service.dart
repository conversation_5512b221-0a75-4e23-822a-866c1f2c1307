import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/legal_review.dart';

/// ⚖️ EXTERNAL LEGAL REVIEW SERVICE - Externí právní kontrola
class ExternalLegalReviewService {
  static final ExternalLegalReviewService _instance =
      ExternalLegalReviewService._internal();
  factory ExternalLegalReviewService() => _instance;
  ExternalLegalReviewService._internal();

  bool _isInitialized = false;
  final List<LegalReviewRequest> _reviewRequests = [];
  final List<LegalExpert> _legalExperts = [];
  final Map<String, LegalReviewReport> _reviewReports = {};
  final StreamController<LegalReviewEvent> _eventController =
      StreamController.broadcast();

  /// Stream právních událostí
  Stream<LegalReviewEvent> get legalEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('⚖️ Inicializuji External Legal Review Service...');

      await _loadLegalExperts();
      await _loadReviewRequests();

      _isInitialized = true;
      debugPrint('✅ External Legal Review Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Legal Review: $e');
      await _createDefaultExperts();
      _isInitialized = true;
    }
  }

  /// Vytvoření požadavku na právní kontrolu
  Future<LegalReviewRequest> createReviewRequest({
    required LegalReviewType type,
    required String title,
    required String description,
    required List<String> documentsToReview,
    LegalExpertise? requiredExpertise,
    Priority priority = Priority.medium,
    DateTime? deadline,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      final request = LegalReviewRequest(
        id: 'review_${DateTime.now().millisecondsSinceEpoch}',
        type: type,
        title: title,
        description: description,
        documentsToReview: documentsToReview,
        requiredExpertise: requiredExpertise,
        priority: priority,
        status: ReviewStatus.pending,
        createdAt: DateTime.now(),
        deadline: deadline,
        additionalInfo: additionalInfo ?? {},
      );

      _reviewRequests.add(request);
      await _saveReviewRequests();

      // Automatické přiřazení experta
      final assignedExpert = await _assignExpert(request);
      if (assignedExpert != null) {
        await _updateRequestStatus(
          request.id,
          ReviewStatus.assigned,
          assignedExpert.id,
        );
      }

      _eventController.add(
        LegalReviewEvent(
          type: LegalEventType.reviewRequested,
          requestId: request.id,
          timestamp: DateTime.now(),
          data: {
            'reviewType': type.name,
            'priority': priority.name,
            'assignedExpert': assignedExpert?.id,
          },
        ),
      );

      debugPrint('⚖️ Právní kontrola požadována: ${request.title}');
      return request;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření legal review: $e');
      rethrow;
    }
  }

  /// Kompletní GDPR compliance review
  Future<LegalReviewRequest> requestGDPRComplianceReview() async {
    return await createReviewRequest(
      type: LegalReviewType.gdprCompliance,
      title: 'GDPR Compliance Review - Croatia Travel App',
      description: '''
Kompletní kontrola GDPR compliance pro Croatia Travel App:

1. Privacy Policy - kontrola úplnosti a souladu s GDPR
2. Terms of Service - právní závaznost a ochrana
3. Consent Management - mechanismy souhlasu
4. Data Processing - zákonnost zpracování
5. User Rights - implementace práv subjektů údajů
6. Data Retention - politiky uchovávání dat
7. Third-party Integrations - smlouvy o zpracování
8. Breach Notification - postupy při narušení

Prioritní kontrola před spuštěním aplikace.
      ''',
      documentsToReview: [
        'Privacy Policy',
        'Terms of Service',
        'Cookie Policy',
        'Data Processing Agreement',
        'Consent Management Implementation',
        'Integration Agreements',
        'Security Policies',
      ],
      requiredExpertise: LegalExpertise.dataProtection,
      priority: Priority.critical,
      deadline: DateTime.now().add(const Duration(days: 7)),
      additionalInfo: {
        'targetMarkets': ['Czech Republic', 'Slovakia', 'Croatia', 'EU'],
        'userTypes': ['B2C', 'B2B'],
        'dataTypes': ['Personal', 'Location', 'Photos', 'Audio', 'Analytics'],
        'thirdParties': ['Google', 'Facebook', 'Apple', 'Cloud providers'],
      },
    );
  }

  /// Kontrola obchodních podmínek
  Future<LegalReviewRequest> requestTermsOfServiceReview() async {
    return await createReviewRequest(
      type: LegalReviewType.termsOfService,
      title: 'Terms of Service Legal Review',
      description: '''
Právní kontrola obchodních podmínek:

1. Právní závaznost podmínek
2. Ochrana před odpovědností
3. Platební podmínky a refundace
4. Duševní vlastnictví
5. Ukončení služby
6. Rozhodné právo a jurisdikce
7. Řešení sporů
8. Compliance s lokálními zákony

Zaměření na české, slovenské a chorvatské právo.
      ''',
      documentsToReview: [
        'Terms of Service',
        'Payment Terms',
        'Refund Policy',
        'Intellectual Property Policy',
        'User Agreement',
      ],
      requiredExpertise: LegalExpertise.contractLaw,
      priority: Priority.high,
      deadline: DateTime.now().add(const Duration(days: 5)),
      additionalInfo: {
        'jurisdictions': ['Czech Republic', 'Slovakia', 'Croatia'],
        'businessModel': 'Freemium SaaS',
        'paymentMethods': ['Credit Card', 'PayPal', 'Apple Pay', 'Google Pay'],
      },
    );
  }

  /// Kontrola autorských práv a licencí
  Future<LegalReviewRequest> requestCopyrightReview() async {
    return await createReviewRequest(
      type: LegalReviewType.intellectualProperty,
      title: 'Copyright and Licensing Review',
      description: '''
Kontrola autorských práv a licencí:

1. Croatian Local Intelligence - ověření zdrojů dat
2. Fotografie a media obsah - licence a attribution
3. Třetí strany API - licenční podmínky
4. Open source komponenty - GPL/MIT/Apache licence
5. Fonty a ikony - komerční použití
6. Mapové podklady - licenční podmínky
7. Audio a video obsah - autorská práva
8. Databáze a data - sui generis práva

Zaměření na eliminaci copyright rizik.
      ''',
      documentsToReview: [
        'Croatian Local Intelligence Sources',
        'Third-party API Agreements',
        'Open Source License Audit',
        'Media Content Licenses',
        'Attribution Requirements',
      ],
      requiredExpertise: LegalExpertise.intellectualProperty,
      priority: Priority.high,
      deadline: DateTime.now().add(const Duration(days: 10)),
      additionalInfo: {
        'contentTypes': ['Text', 'Images', 'Audio', 'Video', 'Data'],
        'sources': ['Tourism boards', 'Open data', 'APIs', 'User generated'],
        'usageType': 'Commercial application',
      },
    );
  }

  /// Kontrola B2B smluv
  Future<LegalReviewRequest> requestB2BContractReview() async {
    return await createReviewRequest(
      type: LegalReviewType.contractLaw,
      title: 'B2B Contract Templates Review',
      description: '''
Kontrola B2B smluvních šablon:

1. Enterprise License Agreement
2. Data Processing Agreement (DPA)
3. Service Level Agreement (SLA)
4. Professional Services Agreement
5. Partnership Agreements
6. Reseller Agreements
7. API Usage Agreements
8. Custom Development Contracts

Zaměření na ochranu a compliance.
      ''',
      documentsToReview: [
        'Enterprise License Template',
        'Data Processing Agreement',
        'Service Level Agreement',
        'Partnership Agreement Template',
        'API Terms of Use',
      ],
      requiredExpertise: LegalExpertise.contractLaw,
      priority: Priority.medium,
      deadline: DateTime.now().add(const Duration(days: 14)),
      additionalInfo: {
        'clientTypes': [
          'Tourism boards',
          'Travel agencies',
          'Hotels',
          'Enterprises',
        ],
        'serviceTypes': ['SaaS', 'Professional services', 'Custom development'],
        'dataProcessing': true,
      },
    );
  }

  /// Aktualizace statusu kontroly
  Future<bool> updateReviewStatus({
    required String requestId,
    required ReviewStatus newStatus,
    String? expertId,
    String? notes,
  }) async {
    try {
      await _updateRequestStatus(requestId, newStatus, expertId, notes);

      _eventController.add(
        LegalReviewEvent(
          type: LegalEventType.statusUpdated,
          requestId: requestId,
          timestamp: DateTime.now(),
          data: {
            'newStatus': newStatus.name,
            'expertId': expertId,
            'notes': notes,
          },
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při aktualizaci statusu: $e');
      return false;
    }
  }

  /// Dokončení kontroly s reportem
  Future<LegalReviewReport> completeReview({
    required String requestId,
    required List<LegalFinding> findings,
    required ComplianceStatus complianceStatus,
    required List<String> recommendations,
    String? summary,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final request = _reviewRequests.firstWhere((r) => r.id == requestId);

      final report = LegalReviewReport(
        id: 'report_${DateTime.now().millisecondsSinceEpoch}',
        requestId: requestId,
        expertId: request.assignedExpertId!,
        findings: findings,
        complianceStatus: complianceStatus,
        recommendations: recommendations,
        summary: summary ?? 'Legal review completed',
        completedAt: DateTime.now(),
        additionalData: additionalData ?? {},
      );

      _reviewReports[requestId] = report;
      await _updateRequestStatus(requestId, ReviewStatus.completed);
      await _saveReviewReports();

      _eventController.add(
        LegalReviewEvent(
          type: LegalEventType.reviewCompleted,
          requestId: requestId,
          timestamp: DateTime.now(),
          data: {
            'complianceStatus': complianceStatus.name,
            'findingsCount': findings.length,
            'criticalIssues': findings
                .where((f) => f.severity == FindingSeverity.critical)
                .length,
          },
        ),
      );

      debugPrint('✅ Legal review dokončen: $requestId');
      return report;
    } catch (e) {
      debugPrint('❌ Chyba při dokončování review: $e');
      rethrow;
    }
  }

  /// Získání statusu všech kontrol
  Future<LegalComplianceOverview> getComplianceOverview() async {
    final totalRequests = _reviewRequests.length;
    final completedRequests = _reviewRequests
        .where((r) => r.status == ReviewStatus.completed)
        .length;
    final pendingRequests = _reviewRequests
        .where((r) => r.status == ReviewStatus.pending)
        .length;
    final inProgressRequests = _reviewRequests
        .where((r) => r.status == ReviewStatus.inProgress)
        .length;

    final criticalIssues = _reviewReports.values
        .expand((r) => r.findings)
        .where((f) => f.severity == FindingSeverity.critical)
        .length;

    final highIssues = _reviewReports.values
        .expand((r) => r.findings)
        .where((f) => f.severity == FindingSeverity.high)
        .length;

    // Výpočet compliance score
    final totalFindings = _reviewReports.values
        .expand((r) => r.findings)
        .length;
    final complianceScore = totalFindings == 0
        ? 100.0
        : ((totalFindings - criticalIssues * 3 - highIssues * 2) /
                  totalFindings *
                  100)
              .clamp(0.0, 100.0);

    return LegalComplianceOverview(
      totalRequests: totalRequests,
      completedRequests: completedRequests,
      pendingRequests: pendingRequests,
      inProgressRequests: inProgressRequests,
      criticalIssues: criticalIssues,
      highIssues: highIssues,
      complianceScore: complianceScore,
      lastReviewDate: _reviewReports.values.isNotEmpty
          ? _reviewReports.values
                .map((r) => r.completedAt)
                .reduce((a, b) => a.isAfter(b) ? a : b)
          : null,
      upcomingDeadlines: _getUpcomingDeadlines(),
      expertUtilization: _calculateExpertUtilization(),
    );
  }

  /// Privátní pomocné metody
  Future<LegalExpert?> _assignExpert(LegalReviewRequest request) async {
    // Najít experta s odpovídající expertízou
    final suitableExperts = _legalExperts.where((expert) {
      return expert.expertise.contains(
            request.requiredExpertise ?? LegalExpertise.general,
          ) &&
          expert.isAvailable;
    }).toList();

    if (suitableExperts.isEmpty) {
      // Fallback na obecného experta
      final generalExperts = _legalExperts
          .where(
            (e) =>
                e.expertise.contains(LegalExpertise.general) && e.isAvailable,
          )
          .toList();

      return generalExperts.isNotEmpty ? generalExperts.first : null;
    }

    // Vybrat experta s nejnižší zátěží
    suitableExperts.sort(
      (a, b) => a.currentWorkload.compareTo(b.currentWorkload),
    );
    return suitableExperts.first;
  }

  Future<void> _updateRequestStatus(
    String requestId,
    ReviewStatus status, [
    String? expertId,
    String? notes,
  ]) async {
    final index = _reviewRequests.indexWhere((r) => r.id == requestId);
    if (index >= 0) {
      _reviewRequests[index] = _reviewRequests[index].copyWith(
        status: status,
        assignedExpertId: expertId ?? _reviewRequests[index].assignedExpertId,
        lastUpdated: DateTime.now(),
        notes: notes,
      );
      await _saveReviewRequests();
    }
  }

  List<DateTime> _getUpcomingDeadlines() {
    return _reviewRequests
        .where((r) => r.deadline != null && r.status != ReviewStatus.completed)
        .map((r) => r.deadline!)
        .where((d) => d.isAfter(DateTime.now()))
        .toList()
      ..sort();
  }

  Map<String, double> _calculateExpertUtilization() {
    final utilization = <String, double>{};

    for (final expert in _legalExperts) {
      final assignedRequests = _reviewRequests
          .where(
            (r) =>
                r.assignedExpertId == expert.id &&
                r.status != ReviewStatus.completed,
          )
          .length;

      utilization[expert.name] =
          (assignedRequests / expert.maxConcurrentReviews * 100).clamp(
            0.0,
            100.0,
          );
    }

    return utilization;
  }

  /// Načítání a ukládání dat
  Future<void> _loadLegalExperts() async {
    await _createDefaultExperts();
  }

  Future<void> _loadReviewRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final requestsJson = prefs.getString('legal_review_requests');

      if (requestsJson != null) {
        final List<dynamic> data = jsonDecode(requestsJson);
        _reviewRequests.clear();
        _reviewRequests.addAll(
          data.map((json) => LegalReviewRequest.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání review requests: $e');
    }
  }

  Future<void> _saveReviewRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'legal_review_requests',
        jsonEncode(_reviewRequests.map((r) => r.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání review requests: $e');
    }
  }

  Future<void> _saveReviewReports() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'legal_review_reports',
        jsonEncode(_reviewReports.map((k, v) => MapEntry(k, v.toJson()))),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání review reports: $e');
    }
  }

  Future<void> _createDefaultExperts() async {
    _legalExperts.addAll([
      LegalExpert(
        id: 'expert_gdpr_001',
        name: 'JUDr. Anna Nováková',
        expertise: [LegalExpertise.dataProtection, LegalExpertise.general],
        specialization: 'GDPR a ochrana osobních údajů',
        experience: 8,
        hourlyRate: 2500.0,
        isAvailable: true,
        maxConcurrentReviews: 3,
        currentWorkload: 1,
        languages: ['Czech', 'English', 'German'],
        certifications: ['CIPP/E', 'CIPM', 'FIP'],
        contactInfo: '<EMAIL>',
      ),
      LegalExpert(
        id: 'expert_contract_001',
        name: 'JUDr. Petr Svoboda',
        email: '<EMAIL>',
        expertise: [LegalExpertise.contracts],
        experienceYears: 12,
        rating: 4.7,
        isAvailable: true,
        currentWorkload: 2,
        certifications: ['Advokát ČAK'],
        bio: 'Obchodní právo a smlouvy specialist',
      ),
      LegalExpert(
        id: 'expert_ip_001',
        name: 'JUDr. Marie Černá',
        email: '<EMAIL>',
        expertise: [LegalExpertise.intellectual],
        experienceYears: 10,
        rating: 4.9,
        isAvailable: true,
        currentWorkload: 0,
        certifications: ['Advokát ČAK', 'IP Specialist'],
        bio: 'Duševní vlastnictví a autorská práva specialist',
      ),
    ]);
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<LegalReviewRequest> get reviewRequests =>
      List.unmodifiable(_reviewRequests);
  List<LegalExpert> get legalExperts => List.unmodifiable(_legalExperts);
  Map<String, LegalReviewReport> get reviewReports =>
      Map.unmodifiable(_reviewReports);
}
