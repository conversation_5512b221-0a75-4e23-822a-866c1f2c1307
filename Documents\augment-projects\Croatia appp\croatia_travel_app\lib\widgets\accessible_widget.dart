import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/accessibility_service.dart';

/// Wrapper widget pro accessibility features
class AccessibleWidget extends StatelessWidget {
  final Widget child;
  final String? semanticLabel;
  final String? hint;
  final bool excludeSemantics;
  final VoidCallback? onTap;
  final bool isFocusable;
  final bool isButton;

  const AccessibleWidget({
    super.key,
    required this.child,
    this.semanticLabel,
    this.hint,
    this.excludeSemantics = false,
    this.onTap,
    this.isFocusable = false,
    this.isButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService();
    
    Widget widget = child;

    // Přidat haptic feedback pro tlačítka
    if (isButton && onTap != null) {
      widget = GestureDetector(
        onTap: () {
          accessibilityService.performHapticFeedback('selection');
          onTap!();
        },
        child: widget,
      );
    }

    // Přidat semantic informace
    if (!excludeSemantics) {
      widget = Semantics(
        label: semanticLabel,
        hint: hint,
        button: isButton,
        focusable: isFocusable,
        child: widget,
      );
    }

    return widget;
  }
}

/// Accessible text widget s automatickým škálováním
class AccessibleText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final String? semanticLabel;
  final bool isLargeText;

  const AccessibleText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.semanticLabel,
    this.isLargeText = false,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService();
    final scaleFactor = accessibilityService.getTextScaleFactor();
    
    TextStyle effectiveStyle = style ?? const TextStyle();
    
    // Aplikovat škálování textu
    if (effectiveStyle.fontSize != null) {
      effectiveStyle = effectiveStyle.copyWith(
        fontSize: effectiveStyle.fontSize! * scaleFactor,
      );
    }

    // Upravit barvy pro barvoslepé uživatele
    if (effectiveStyle.color != null) {
      effectiveStyle = effectiveStyle.copyWith(
        color: accessibilityService.adjustColorForColorBlind(effectiveStyle.color!),
      );
    }

    return Semantics(
      label: semanticLabel ?? text,
      child: Text(
        text,
        style: effectiveStyle,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// Accessible button s optimální velikostí touch targetu
class AccessibleButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String? semanticLabel;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool isPrimary;

  const AccessibleButton({
    super.key,
    required this.child,
    this.onPressed,
    this.semanticLabel,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.isPrimary = false,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService();
    
    // Minimální velikost touch targetu (44x44 podle WCAG)
    const minTouchTarget = 44.0;
    
    Color effectiveBackgroundColor = backgroundColor ?? 
        (isPrimary ? const Color(0xFF006994) : Colors.grey[200]!);
    Color effectiveForegroundColor = foregroundColor ?? 
        (isPrimary ? Colors.white : const Color(0xFF2C2C2C));

    // Upravit barvy pro accessibility
    effectiveBackgroundColor = accessibilityService.adjustColorForColorBlind(effectiveBackgroundColor);
    effectiveForegroundColor = accessibilityService.adjustColorForColorBlind(effectiveForegroundColor);

    // Vysoký kontrast
    if (accessibilityService.highContrastMode) {
      effectiveBackgroundColor = isPrimary ? Colors.black : Colors.white;
      effectiveForegroundColor = isPrimary ? Colors.white : Colors.black;
    }

    Widget button = Container(
      constraints: const BoxConstraints(
        minWidth: minTouchTarget,
        minHeight: minTouchTarget,
      ),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        border: accessibilityService.highContrastMode 
            ? Border.all(color: Colors.black, width: 2)
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed != null ? () {
            accessibilityService.performHapticFeedback('selection');
            onPressed!();
          } : null,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          child: Padding(
            padding: padding ?? const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            child: DefaultTextStyle(
              style: TextStyle(
                color: effectiveForegroundColor,
                fontSize: 16 * accessibilityService.getTextScaleFactor(),
              ),
              child: child,
            ),
          ),
        ),
      ),
    );

    // Přidat tooltip pokud je zadán
    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    // Přidat semantic informace
    return Semantics(
      label: semanticLabel,
      button: true,
      enabled: onPressed != null,
      child: button,
    );
  }
}

/// Accessible icon s popisem pro screen readery
class AccessibleIcon extends StatelessWidget {
  final IconData icon;
  final String semanticLabel;
  final double? size;
  final Color? color;

  const AccessibleIcon({
    super.key,
    required this.icon,
    required this.semanticLabel,
    this.size,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService();
    
    Color effectiveColor = color ?? const Color(0xFF2C2C2C);
    effectiveColor = accessibilityService.adjustColorForColorBlind(effectiveColor);

    if (accessibilityService.highContrastMode) {
      effectiveColor = Colors.black;
    }

    return Semantics(
      label: semanticLabel,
      child: ExcludeSemantics(
        child: Icon(
          icon,
          size: size,
          color: effectiveColor,
        ),
      ),
    );
  }
}

/// Accessible card s focus indikátorem
class AccessibleCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const AccessibleCard({
    super.key,
    required this.child,
    this.onTap,
    this.semanticLabel,
    this.padding,
    this.margin,
  });

  @override
  State<AccessibleCard> createState() => _AccessibleCardState();
}

class _AccessibleCardState extends State<AccessibleCard> {
  bool _isFocused = false;

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService();
    
    return Container(
      margin: widget.margin ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: _isFocused && accessibilityService.screenReaderEnabled
            ? Border.all(color: const Color(0xFF006994), width: 3)
            : Border.all(color: Colors.grey[300]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Focus(
        onFocusChange: (focused) {
          setState(() {
            _isFocused = focused;
          });
        },
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: widget.onTap != null ? () {
              accessibilityService.performHapticFeedback('light');
              widget.onTap!();
            } : null,
            borderRadius: BorderRadius.circular(12),
            child: Semantics(
              label: widget.semanticLabel,
              button: widget.onTap != null,
              child: Padding(
                padding: widget.padding ?? const EdgeInsets.all(16),
                child: widget.child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Mixin pro accessibility v custom widgetech
mixin AccessibilityMixin<T extends StatefulWidget> on State<T> {
  AccessibilityService get accessibilityService => AccessibilityService();

  /// Provede haptic feedback
  void performHapticFeedback([String type = 'light']) {
    accessibilityService.performHapticFeedback(type);
  }

  /// Získá škálovanou velikost textu
  double getScaledFontSize(double originalSize) {
    return originalSize * accessibilityService.getTextScaleFactor();
  }

  /// Upraví barvu pro accessibility
  Color adjustColor(Color originalColor) {
    return accessibilityService.adjustColorForColorBlind(originalColor);
  }

  /// Získá duration pro animace
  Duration getAnimationDuration(Duration originalDuration) {
    return accessibilityService.getAnimationDuration(originalDuration);
  }

  /// Zkontroluje, zda jsou animace povolené
  bool get areAnimationsEnabled => accessibilityService.areAnimationsEnabled();

  /// Vytvoří semantic label
  String createSemanticLabel(String text, {String? context, String? state}) {
    return accessibilityService.getAccessibilityLabel(text, context: context);
  }
}
