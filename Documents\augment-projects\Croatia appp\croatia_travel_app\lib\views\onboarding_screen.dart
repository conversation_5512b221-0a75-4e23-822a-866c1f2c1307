import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Onboarding screen pro nové uživatele
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Vítejte v Adriatic Diary',
      subtitle: '<PERSON><PERSON><PERSON> osobní průvodce Chorvatskem',
      description: 'Objevte nejkrásnější místa, vytvořte nezapomenutelné vzpomínky a nechte se inspirovat krásou Jadranu.',
      icon: Icons.book_outlined,
      gradient: [
        Color(0xFF006994),
        Color(0xFF2E8B8B),
      ],
    ),
    OnboardingPage(
      title: 'Objevujte místa',
      subtitle: '<PERSON><PERSON><PERSON><PERSON> destinac<PERSON>',
      description: 'Od historick<PERSON>ch měst po skryté pláže. Najděte místa podle vašich zájmů a vytvořte si vlastní cestovní plán.',
      icon: Icons.place_outlined,
      gradient: [
        Color(0xFF2E8B8B),
        Color(0xFF006994),
      ],
    ),
    OnboardingPage(
      title: 'Pište deník',
      subtitle: 'Zachyťte každý okamžik',
      description: 'Zaznamenejte své zážitky, přidejte fotografie a vytvořte si krásný cestovní deník na celý život.',
      icon: Icons.edit_outlined,
      gradient: [
        Color(0xFF006994),
        Color(0xFFFF6B35),
      ],
    ),
    OnboardingPage(
      title: 'Začněte cestovat',
      subtitle: 'Vaše dobrodružství začíná teď',
      description: 'Máte vše připraveno! Začněte objevovat Chorvatsko s vaším osobním průvodcem.',
      icon: Icons.explore_outlined,
      gradient: [
        Color(0xFFFF6B35),
        Color(0xFF2E8B8B),
      ],
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeOnboarding() {
    // Uložit, že onboarding byl dokončen
    Navigator.pushReplacementNamed(context, '/main');
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F6F0),
      body: SafeArea(
        child: Column(
          children: [
            // Skip tlačítko
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (_currentPage < _pages.length - 1)
                    TextButton(
                      onPressed: _skipOnboarding,
                      child: Text(
                        'Přeskočit',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // PageView s onboarding stránkami
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildOnboardingPage(_pages[index]);
                },
              ),
            ),

            // Indikátory a navigace
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => _buildPageIndicator(index),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Navigační tlačítka
                  Row(
                    children: [
                      // Zpět tlačítko
                      if (_currentPage > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _previousPage,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              side: const BorderSide(
                                color: Color(0xFF006994),
                                width: 2,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Zpět',
                              style: GoogleFonts.inter(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF006994),
                              ),
                            ),
                          ),
                        ),

                      if (_currentPage > 0) const SizedBox(width: 16),

                      // Další/Dokončit tlačítko
                      Expanded(
                        flex: _currentPage > 0 ? 1 : 1,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors: _pages[_currentPage].gradient,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: _pages[_currentPage].gradient[0]
                                    .withValues(alpha: 0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _nextPage,
                              borderRadius: BorderRadius.circular(12),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      _currentPage < _pages.length - 1
                                          ? 'Další'
                                          : 'Začít',
                                      style: GoogleFonts.inter(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Icon(
                                      _currentPage < _pages.length - 1
                                          ? Icons.arrow_forward
                                          : Icons.check,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Ikona s gradient pozadím
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: page.gradient.map((c) => c.withValues(alpha: 0.2)).toList(),
              ),
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: page.gradient[0],
            ),
          ),

          const SizedBox(height: 48),

          // Titulek
          Text(
            page.title,
            style: GoogleFonts.playfairDisplay(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C2C2C),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Podtitulek
          Text(
            page.subtitle,
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: page.gradient[0],
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Popis
          Text(
            page.description,
            style: GoogleFonts.inter(
              fontSize: 16,
              color: const Color(0xFF666666),
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final isActive = index == _currentPage;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: isActive
            ? _pages[_currentPage].gradient[0]
            : _pages[_currentPage].gradient[0].withValues(alpha: 0.3),
      ),
    );
  }
}

/// Model pro onboarding stránku
class OnboardingPage {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final List<Color> gradient;

  OnboardingPage({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.gradient,
  });
}
