import 'package:json_annotation/json_annotation.dart';

part 'gamification_onboarding.g.dart';

/// 🎮 GAMIFICATION ONBOARDING MODELS - Modely pro gamifikovaný onboarding

/// Typ onboarding události
enum OnboardingEventType {
  questStarted,
  questCompleted,
  milestoneReached,
  journeyCompleted,
  achievementUnlocked,
}

/// Personalita onboardingu
enum OnboardingPersonality {
  explorer,
  achiever,
  socializer,
  competitor,
}

/// Trigger pro achievement
enum AchievementTrigger {
  questCompletion,
  timeSpent,
  featureUsage,
  socialInteraction,
  streakMaintained,
}

/// Onboarding quest
@JsonSerializable()
class OnboardingQuest {
  final String id;
  final String title;
  final String description;
  final String category;
  final int difficulty;
  final int estimatedMinutes;
  final List<String> steps;
  final Map<String, dynamic> requirements;
  final Map<String, dynamic> rewards;
  final bool isOptional;
  final List<String> prerequisites;

  const OnboardingQuest({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.estimatedMinutes,
    required this.steps,
    required this.requirements,
    required this.rewards,
    required this.isOptional,
    required this.prerequisites,
  });

  factory OnboardingQuest.fromJson(Map<String, dynamic> json) =>
      _$OnboardingQuestFromJson(json);
  Map<String, dynamic> toJson() => _$OnboardingQuestToJson(this);
}

/// Onboarding milestone
@JsonSerializable()
class OnboardingMilestone {
  final String id;
  final String title;
  final String description;
  final int requiredQuests;
  final List<String> questIds;
  final Map<String, dynamic> rewards;
  final String? badgeIcon;
  final bool isCompleted;

  const OnboardingMilestone({
    required this.id,
    required this.title,
    required this.description,
    required this.requiredQuests,
    required this.questIds,
    required this.rewards,
    this.badgeIcon,
    required this.isCompleted,
  });

  factory OnboardingMilestone.fromJson(Map<String, dynamic> json) =>
      _$OnboardingMilestoneFromJson(json);
  Map<String, dynamic> toJson() => _$OnboardingMilestoneToJson(this);
}

/// Progress uživatele v onboardingu
@JsonSerializable()
class UserOnboardingProgress {
  final String userId;
  final String journeyId;
  final List<String> completedQuests;
  final List<String> completedMilestones;
  final int totalPoints;
  final int currentStreak;
  final DateTime startedAt;
  final DateTime? completedAt;
  final OnboardingPersonality personality;
  final Map<String, dynamic> preferences;
  final Map<String, int> categoryProgress;

  const UserOnboardingProgress({
    required this.userId,
    required this.journeyId,
    required this.completedQuests,
    required this.completedMilestones,
    required this.totalPoints,
    required this.currentStreak,
    required this.startedAt,
    this.completedAt,
    required this.personality,
    required this.preferences,
    required this.categoryProgress,
  });

  factory UserOnboardingProgress.fromJson(Map<String, dynamic> json) =>
      _$UserOnboardingProgressFromJson(json);
  Map<String, dynamic> toJson() => _$UserOnboardingProgressToJson(this);
}

/// Onboarding journey
@JsonSerializable()
class OnboardingJourney {
  final String id;
  final String title;
  final String description;
  final List<OnboardingQuest> quests;
  final List<OnboardingMilestone> milestones;
  final int estimatedDays;
  final OnboardingPersonality targetPersonality;
  final Map<String, dynamic> configuration;

  const OnboardingJourney({
    required this.id,
    required this.title,
    required this.description,
    required this.quests,
    required this.milestones,
    required this.estimatedDays,
    required this.targetPersonality,
    required this.configuration,
  });

  factory OnboardingJourney.fromJson(Map<String, dynamic> json) =>
      _$OnboardingJourneyFromJson(json);
  Map<String, dynamic> toJson() => _$OnboardingJourneyToJson(this);
}

/// Výsledek dokončení questu
@JsonSerializable()
class QuestCompletionResult {
  final String questId;
  final String userId;
  final DateTime completedAt;
  final int pointsEarned;
  final List<String> unlockedFeatures;
  final List<String> earnedBadges;
  final bool triggeredMilestone;
  final String? nextRecommendedQuest;

  const QuestCompletionResult({
    required this.questId,
    required this.userId,
    required this.completedAt,
    required this.pointsEarned,
    required this.unlockedFeatures,
    required this.earnedBadges,
    required this.triggeredMilestone,
    this.nextRecommendedQuest,
  });

  factory QuestCompletionResult.fromJson(Map<String, dynamic> json) =>
      _$QuestCompletionResultFromJson(json);
  Map<String, dynamic> toJson() => _$QuestCompletionResultToJson(this);
}

/// Onboarding událost
@JsonSerializable()
class OnboardingEvent {
  final String id;
  final OnboardingEventType type;
  final String userId;
  final String? questId;
  final String? milestoneId;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const OnboardingEvent({
    required this.id,
    required this.type,
    required this.userId,
    this.questId,
    this.milestoneId,
    required this.timestamp,
    required this.data,
  });

  factory OnboardingEvent.fromJson(Map<String, dynamic> json) =>
      _$OnboardingEventFromJson(json);
  Map<String, dynamic> toJson() => _$OnboardingEventToJson(this);
}

/// Odměna za dokončení onboardingu
@JsonSerializable()
class OnboardingCompletionReward {
  final String id;
  final String title;
  final String description;
  final String type; // 'feature', 'discount', 'badge', 'points'
  final Map<String, dynamic> value;
  final DateTime? expiresAt;
  final bool isRedeemed;

  const OnboardingCompletionReward({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.value,
    this.expiresAt,
    required this.isRedeemed,
  });

  factory OnboardingCompletionReward.fromJson(Map<String, dynamic> json) =>
      _$OnboardingCompletionRewardFromJson(json);
  Map<String, dynamic> toJson() => _$OnboardingCompletionRewardToJson(this);
}
