import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/image_cache_service.dart';
import '../widgets/skeleton_loading.dart';

/// Optimalizovaný image widget s lazy loading a cachováním
class OptimizedImage extends StatefulWidget {
  final String? imageUrl;
  final String? assetPath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;
  final bool enableLazyLoading;
  final Duration fadeInDuration;

  const OptimizedImage({
    super.key,
    this.imageUrl,
    this.assetPath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
    this.enableLazyLoading = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
  }) : assert(imageUrl != null || assetPath != null, 'Either imageUrl or assetPath must be provided');

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage>
    with SingleTickerProviderStateMixin {
  final ImageCacheService _cacheService = ImageCacheService();
  
  bool _isVisible = false;
  bool _isLoading = false;
  bool _hasError = false;
  Uint8List? _imageData;
  
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: widget.fadeInDuration,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    if (!widget.enableLazyLoading) {
      _loadImage();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  void _onVisibilityChanged(bool isVisible) {
    if (isVisible && !_isVisible && widget.enableLazyLoading) {
      setState(() {
        _isVisible = true;
      });
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (_isLoading || _imageData != null) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      if (widget.assetPath != null) {
        // Načtení asset obrázku
        await _loadAssetImage();
      } else if (widget.imageUrl != null) {
        // Načtení network obrázku s cachováním
        await _loadNetworkImage();
      }

      if (_imageData != null) {
        _fadeController.forward();
      }
    } catch (e) {
      setState(() {
        _hasError = true;
      });
      debugPrint('Chyba při načítání obrázku: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAssetImage() async {
    try {
      final ByteData data = await rootBundle.load(widget.assetPath!);
      setState(() {
        _imageData = data.buffer.asUint8List();
      });
    } catch (e) {
      throw Exception('Nepodařilo se načíst asset obrázek: $e');
    }
  }

  Future<void> _loadNetworkImage() async {
    try {
      // Zkusit načíst z cache
      final cachedData = await _cacheService.getCachedImage(widget.imageUrl!);
      if (cachedData != null) {
        setState(() {
          _imageData = cachedData;
        });
        return;
      }

      // Načíst z internetu (simulace - v reálné aplikaci by se použil http)
      // Pro demo účely použijeme placeholder data
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Simulace network obrázku
      final ByteData data = await rootBundle.load('assets/images/placeholder.jpg');
      final imageData = data.buffer.asUint8List();
      
      // Uložit do cache
      await _cacheService.cacheImage(widget.imageUrl!, imageData);
      
      setState(() {
        _imageData = imageData;
      });
    } catch (e) {
      throw Exception('Nepodařilo se načíst network obrázek: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.enableLazyLoading) {
      return VisibilityDetector(
        key: Key(widget.imageUrl ?? widget.assetPath ?? 'optimized_image'),
        onVisibilityChanged: (info) {
          _onVisibilityChanged(info.visibleFraction > 0.1);
        },
        child: _buildImageWidget(),
      );
    }

    return _buildImageWidget();
  }

  Widget _buildImageWidget() {
    Widget child;

    if (_hasError) {
      child = _buildErrorWidget();
    } else if (_imageData != null) {
      child = _buildImageContent();
    } else if (_isLoading || widget.enableLazyLoading && !_isVisible) {
      child = _buildPlaceholder();
    } else {
      child = _buildPlaceholder();
    }

    if (widget.borderRadius != null) {
      child = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: child,
      );
    }

    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: child,
    );
  }

  Widget _buildImageContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Image.memory(
        _imageData!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorWidget();
        },
      ),
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return SkeletonLoading(
      width: widget.width,
      height: widget.height,
      borderRadius: widget.borderRadius,
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: widget.borderRadius,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            size: 32,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 8),
          Text(
            'Obrázek se nepodařilo načíst',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Jednoduchý visibility detector pro lazy loading
class VisibilityDetector extends StatefulWidget {
  final Key key;
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;

  const VisibilityDetector({
    required this.key,
    required this.child,
    required this.onVisibilityChanged,
  }) : super(key: key);

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  Widget build(BuildContext context) {
    // Zjednodušená implementace - v reálné aplikaci by se použila
    // knihovna jako visibility_detector
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        // Simulace visibility detection
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onVisibilityChanged(VisibilityInfo(visibleFraction: 1.0));
        });
        return false;
      },
      child: widget.child,
    );
  }
}

/// Info o viditelnosti widgetu
class VisibilityInfo {
  final double visibleFraction;

  VisibilityInfo({required this.visibleFraction});
}

/// Optimalizovaný network image provider s cachováním
class CachedNetworkImageProvider extends ImageProvider<CachedNetworkImageProvider> {
  final String url;
  final ImageCacheService _cacheService = ImageCacheService();

  CachedNetworkImageProvider(this.url);

  @override
  Future<CachedNetworkImageProvider> obtainKey(ImageConfiguration configuration) {
    return SynchronousFuture<CachedNetworkImageProvider>(this);
  }

  @override
  ImageStreamCompleter load(CachedNetworkImageProvider key, DecoderCallback decode) {
    return MultiFrameImageStreamCompleter(
      codec: _loadAsync(key, decode),
      scale: 1.0,
    );
  }

  Future<Codec> _loadAsync(CachedNetworkImageProvider key, DecoderCallback decode) async {
    try {
      // Zkusit načíst z cache
      final cachedData = await _cacheService.getCachedImage(url);
      if (cachedData != null) {
        return await decode(cachedData);
      }

      // Načíst z internetu (zde by byla skutečná HTTP implementace)
      throw UnimplementedError('Network loading not implemented in demo');
    } catch (e) {
      throw Exception('Failed to load image: $e');
    }
  }

  @override
  bool operator ==(Object other) {
    if (other.runtimeType != runtimeType) return false;
    return other is CachedNetworkImageProvider && other.url == url;
  }

  @override
  int get hashCode => url.hashCode;
}
