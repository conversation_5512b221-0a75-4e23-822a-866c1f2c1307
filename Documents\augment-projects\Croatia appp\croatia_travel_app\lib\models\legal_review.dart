import 'package:json_annotation/json_annotation.dart';

part 'legal_review.g.dart';

/// 📋 LEGAL REVIEW MODELS - Modely pro právní přezkum

/// Typ právního přezkumu
enum LegalReviewType {
  compliance,
  contract,
  privacy,
  terms,
  policy,
  regulation,
}

/// Právní expertíza
enum LegalExpertise {
  gdpr,
  contracts,
  intellectual,
  employment,
  corporate,
  litigation,
}

/// Priorita
enum Priority {
  low,
  medium,
  high,
  urgent,
}

/// Status přezkumu
enum ReviewStatus {
  pending,
  inProgress,
  completed,
  rejected,
  onHold,
}

/// Typ právní události
enum LegalEventType {
  reviewRequested,
  expertAssigned,
  reviewStarted,
  reviewCompleted,
  reportGenerated,
}

/// Status compliance
enum ComplianceStatus {
  compliant,
  nonCompliant,
  partiallyCompliant,
  unknown,
}

/// Závažnost nálezu
enum FindingSeverity {
  low,
  medium,
  high,
  critical,
}

/// Žádost o právní přezkum
@JsonSerializable()
class LegalReviewRequest {
  final String id;
  final String title;
  final String description;
  final LegalReviewType type;
  final List<LegalExpertise> requiredExpertise;
  final Priority priority;
  final DateTime deadline;
  final List<String> documents;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final String requestedBy;
  final ReviewStatus status;
  final String? assignedExpertId;

  const LegalReviewRequest({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.requiredExpertise,
    required this.priority,
    required this.deadline,
    required this.documents,
    required this.metadata,
    required this.createdAt,
    required this.requestedBy,
    required this.status,
    this.assignedExpertId,
  });

  factory LegalReviewRequest.fromJson(Map<String, dynamic> json) =>
      _$LegalReviewRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LegalReviewRequestToJson(this);
}

/// Právní expert
@JsonSerializable()
class LegalExpert {
  final String id;
  final String name;
  final String email;
  final List<LegalExpertise> expertise;
  final int experienceYears;
  final double rating;
  final bool isAvailable;
  final int currentWorkload;
  final List<String> certifications;
  final String? bio;

  const LegalExpert({
    required this.id,
    required this.name,
    required this.email,
    required this.expertise,
    required this.experienceYears,
    required this.rating,
    required this.isAvailable,
    required this.currentWorkload,
    required this.certifications,
    this.bio,
  });

  factory LegalExpert.fromJson(Map<String, dynamic> json) =>
      _$LegalExpertFromJson(json);
  Map<String, dynamic> toJson() => _$LegalExpertToJson(this);
}

/// Právní nález
@JsonSerializable()
class LegalFinding {
  final String id;
  final String title;
  final String description;
  final FindingSeverity severity;
  final String recommendation;
  final List<String> references;
  final bool requiresAction;

  const LegalFinding({
    required this.id,
    required this.title,
    required this.description,
    required this.severity,
    required this.recommendation,
    required this.references,
    required this.requiresAction,
  });

  factory LegalFinding.fromJson(Map<String, dynamic> json) =>
      _$LegalFindingFromJson(json);
  Map<String, dynamic> toJson() => _$LegalFindingToJson(this);
}

/// Zpráva z právního přezkumu
@JsonSerializable()
class LegalReviewReport {
  final String id;
  final String requestId;
  final String expertId;
  final String title;
  final String summary;
  final ComplianceStatus overallStatus;
  final List<LegalFinding> findings;
  final List<String> recommendations;
  final DateTime completedAt;
  final Map<String, dynamic> metadata;

  const LegalReviewReport({
    required this.id,
    required this.requestId,
    required this.expertId,
    required this.title,
    required this.summary,
    required this.overallStatus,
    required this.findings,
    required this.recommendations,
    required this.completedAt,
    required this.metadata,
  });

  factory LegalReviewReport.fromJson(Map<String, dynamic> json) =>
      _$LegalReviewReportFromJson(json);
  Map<String, dynamic> toJson() => _$LegalReviewReportToJson(this);
}

/// Právní událost
@JsonSerializable()
class LegalReviewEvent {
  final String id;
  final LegalEventType type;
  final String requestId;
  final String? expertId;
  final DateTime timestamp;
  final String description;
  final Map<String, dynamic> data;

  const LegalReviewEvent({
    required this.id,
    required this.type,
    required this.requestId,
    this.expertId,
    required this.timestamp,
    required this.description,
    required this.data,
  });

  factory LegalReviewEvent.fromJson(Map<String, dynamic> json) =>
      _$LegalReviewEventFromJson(json);
  Map<String, dynamic> toJson() => _$LegalReviewEventToJson(this);
}

/// Přehled právního compliance
@JsonSerializable()
class LegalComplianceOverview {
  final DateTime reportDate;
  final int totalReviews;
  final int completedReviews;
  final int pendingReviews;
  final int criticalFindings;
  final int highFindings;
  final double overallComplianceScore;
  final List<String> topRisks;
  final List<String> recommendations;

  const LegalComplianceOverview({
    required this.reportDate,
    required this.totalReviews,
    required this.completedReviews,
    required this.pendingReviews,
    required this.criticalFindings,
    required this.highFindings,
    required this.overallComplianceScore,
    required this.topRisks,
    required this.recommendations,
  });

  factory LegalComplianceOverview.fromJson(Map<String, dynamic> json) =>
      _$LegalComplianceOverviewFromJson(json);
  Map<String, dynamic> toJson() => _$LegalComplianceOverviewToJson(this);
}
