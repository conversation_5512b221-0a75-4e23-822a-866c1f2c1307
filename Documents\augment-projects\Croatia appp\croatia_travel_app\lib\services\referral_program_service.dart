import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';

/// 🎯 REFERRAL PROGRAM SERVICE - Největší growth driver
class ReferralProgramService {
  static final ReferralProgramService _instance =
      ReferralProgramService._internal();
  factory ReferralProgramService() => _instance;
  ReferralProgramService._internal();

  bool _isInitialized = false;
  final List<ReferralCode> _referralCodes = [];
  final List<ReferralReward> _rewards = [];
  final Map<String, ReferralStats> _userStats = {};
  final StreamController<ReferralEvent> _eventController =
      StreamController.broadcast();

  /// Stream referral událostí
  Stream<ReferralEvent> get referralEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🎯 Inicializuji Referral Program Service...');

      await _loadReferralData();
      await _setupDefaultRewards();

      _isInitialized = true;
      debugPrint('✅ Referral Program Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Referral Program: $e');
      rethrow;
    }
  }

  /// Vytvoření referral kódu pro uživatele
  Future<String> createReferralCode(String userId) async {
    try {
      debugPrint('🔗 Vytvářím referral kód pro uživatele $userId...');

      // Generování unikátního kódu
      final code = _generateReferralCode(userId);

      final referralCode = ReferralCode(
        id: 'ref_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        code: code,
        createdAt: DateTime.now(),
        isActive: true,
        usageCount: 0,
        maxUsage: 100, // Unlimited pro většinu uživatelů
      );

      _referralCodes.add(referralCode);
      await _saveReferralData();

      // Inicializace stats pro uživatele
      _userStats[userId] = ReferralStats(
        userId: userId,
        totalReferrals: 0,
        successfulReferrals: 0,
        totalRewardsEarned: 0.0,
        currentStreak: 0,
        bestStreak: 0,
        tier: ReferralTier.bronze,
      );

      _eventController.add(
        ReferralEvent(
          type: ReferralEventType.codeCreated,
          userId: userId,
          referralCode: code,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Referral kód vytvořen: $code');
      return code;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření referral kódu: $e');
      rethrow;
    }
  }

  /// Sdílení referral kódu
  Future<bool> shareReferralCode({
    required String userId,
    ShareMethod method = ShareMethod.general,
    Map<String, dynamic>? customMessage,
  }) async {
    try {
      final userCode = await getUserReferralCode(userId);
      if (userCode == null) return false;

      final shareContent = _generateShareContent(
        userCode,
        method,
        customMessage,
      );

      switch (method) {
        case ShareMethod.general:
          await Share.share(shareContent.message);
          break;
        case ShareMethod.whatsapp:
          await _shareToWhatsApp(shareContent);
          break;
        case ShareMethod.instagram:
          await _shareToInstagram(shareContent);
          break;
        case ShareMethod.sms:
          await _shareViaSMS(shareContent);
          break;
        case ShareMethod.email:
          await _shareViaEmail(shareContent);
          break;
      }

      _eventController.add(
        ReferralEvent(
          type: ReferralEventType.codeShared,
          userId: userId,
          referralCode: userCode,
          shareMethod: method,
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při sdílení referral kódu: $e');
      return false;
    }
  }

  /// Použití referral kódu při registraci
  Future<ReferralResult> useReferralCode({
    required String code,
    required String newUserId,
  }) async {
    try {
      debugPrint('🎁 Používám referral kód: $code pro uživatele $newUserId');

      // Najdeme referral kód
      final referralCode = _referralCodes.firstWhere(
        (rc) => rc.code == code && rc.isActive,
        orElse: () => throw Exception('Neplatný referral kód'),
      );

      // Kontrola limitů
      if (referralCode.usageCount >= referralCode.maxUsage) {
        throw Exception('Referral kód dosáhl maximálního počtu použití');
      }

      // Kontrola, že uživatel nepoužívá vlastní kód
      if (referralCode.userId == newUserId) {
        throw Exception('Nemůžete použít vlastní referral kód');
      }

      // Aktualizace usage count
      final codeIndex = _referralCodes.indexWhere(
        (rc) => rc.id == referralCode.id,
      );
      _referralCodes[codeIndex] = referralCode.copyWith(
        usageCount: referralCode.usageCount + 1,
        lastUsedAt: DateTime.now(),
      );

      // Udělení odměn
      final referrerReward = await _grantReferrerReward(referralCode.userId);
      final refereeReward = await _grantRefereeReward(newUserId);

      // Aktualizace statistik
      await _updateReferralStats(referralCode.userId, true);

      await _saveReferralData();

      final result = ReferralResult(
        success: true,
        referrerId: referralCode.userId,
        refereeId: newUserId,
        referrerReward: referrerReward,
        refereeReward: refereeReward,
        bonusReward: await _checkBonusRewards(referralCode.userId),
      );

      _eventController.add(
        ReferralEvent(
          type: ReferralEventType.codeUsed,
          userId: referralCode.userId,
          refereeId: newUserId,
          referralCode: code,
          reward: referrerReward,
          timestamp: DateTime.now(),
        ),
      );

      debugPrint('✅ Referral kód úspěšně použit');
      return result;
    } catch (e) {
      debugPrint('❌ Chyba při použití referral kódu: $e');
      return ReferralResult(success: false, error: e.toString());
    }
  }

  /// Získání referral kódu uživatele
  Future<String?> getUserReferralCode(String userId) async {
    try {
      final userCode = _referralCodes.firstWhere(
        (rc) => rc.userId == userId && rc.isActive,
        orElse: () => throw Exception('Kód nenalezen'),
      );
      return userCode.code;
    } catch (e) {
      // Vytvoříme nový kód pokud neexistuje
      return await createReferralCode(userId);
    }
  }

  /// Získání referral statistik uživatele
  Future<ReferralStats> getUserReferralStats(String userId) async {
    return _userStats[userId] ??
        ReferralStats(
          userId: userId,
          totalReferrals: 0,
          successfulReferrals: 0,
          totalRewardsEarned: 0.0,
          currentStreak: 0,
          bestStreak: 0,
          tier: ReferralTier.bronze,
        );
  }

  /// Získání leaderboard
  Future<List<ReferralLeaderboardEntry>> getLeaderboard({
    LeaderboardType type = LeaderboardType.allTime,
    int limit = 10,
  }) async {
    final entries = <ReferralLeaderboardEntry>[];

    for (final stats in _userStats.values) {
      entries.add(
        ReferralLeaderboardEntry(
          userId: stats.userId,
          userName:
              'User ${stats.userId.substring(0, 8)}', // V produkci by se načetlo skutečné jméno
          referralCount: stats.successfulReferrals,
          rewardsEarned: stats.totalRewardsEarned,
          tier: stats.tier,
          avatar:
              'https://api.dicebear.com/7.x/avataaars/svg?seed=${stats.userId}',
        ),
      );
    }

    // Seřazení podle počtu referrals
    entries.sort((a, b) => b.referralCount.compareTo(a.referralCount));

    return entries.take(limit).toList();
  }

  /// Kontrola dostupných odměn
  Future<List<ReferralReward>> getAvailableRewards(String userId) async {
    final stats = await getUserReferralStats(userId);
    final availableRewards = <ReferralReward>[];

    for (final reward in _rewards) {
      if (reward.requiredReferrals <= stats.successfulReferrals &&
          !reward.claimedBy.contains(userId)) {
        availableRewards.add(reward);
      }
    }

    return availableRewards;
  }

  /// Vyzvednutí odměny
  Future<bool> claimReward({
    required String userId,
    required String rewardId,
  }) async {
    try {
      final rewardIndex = _rewards.indexWhere((r) => r.id == rewardId);
      if (rewardIndex == -1) return false;

      final reward = _rewards[rewardIndex];
      final stats = await getUserReferralStats(userId);

      // Kontrola oprávnění
      if (stats.successfulReferrals < reward.requiredReferrals) return false;
      if (reward.claimedBy.contains(userId)) return false;

      // Přidání do claimed
      _rewards[rewardIndex] = reward.copyWith(
        claimedBy: [...reward.claimedBy, userId],
      );

      // Aktualizace stats
      _userStats[userId] = stats.copyWith(
        totalRewardsEarned: stats.totalRewardsEarned + reward.value,
      );

      await _saveReferralData();

      _eventController.add(
        ReferralEvent(
          type: ReferralEventType.rewardClaimed,
          userId: userId,
          rewardId: rewardId,
          reward: reward,
          timestamp: DateTime.now(),
        ),
      );

      return true;
    } catch (e) {
      debugPrint('❌ Chyba při vyzvedávání odměny: $e');
      return false;
    }
  }

  /// Generování referral kódu
  String _generateReferralCode(String userId) {
    final random = Random();
    final chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final prefix = userId.substring(0, min(2, userId.length)).toUpperCase();
    final suffix = List.generate(
      4,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
    return '$prefix$suffix';
  }

  /// Generování share content
  ShareContent _generateShareContent(
    String referralCode,
    ShareMethod method,
    Map<String, dynamic>? customMessage,
  ) {
    final baseMessage =
        customMessage?['message'] ??
        'Pridruži mi se u Croatia Travel App! 🇭🇷✨\n\n'
            'Koristi moj kod: $referralCode\n\n'
            'Oboje ćemo dobiti nagrade! 🎁\n\n'
            'Preuzmi aplikaciju: https://croatia-travel-app.com/download';

    switch (method) {
      case ShareMethod.whatsapp:
        return ShareContent(
          message:
              '$baseMessage\n\n📱 Najbolja aplikacija za putovanje po hrvatskoj!',
          deepLink: 'https://croatia-travel-app.com/ref/$referralCode',
        );
      case ShareMethod.instagram:
        return ShareContent(
          message:
              'Check out Croatia Travel App! 🇭🇷 Use my code: $referralCode',
          deepLink: 'https://croatia-travel-app.com/ref/$referralCode',
          hashtags: ['#CroatiaTravel', '#TravelApp', '#Croatia'],
        );
      case ShareMethod.sms:
        return ShareContent(
          message:
              'Hej! Preuzmi Croatia Travel App i koristi moj kod $referralCode za nagrade! 🎁\n\nhttps://croatia-travel-app.com/ref/$referralCode',
          deepLink: 'https://croatia-travel-app.com/ref/$referralCode',
        );
      default:
        return ShareContent(
          message: baseMessage,
          deepLink: 'https://croatia-travel-app.com/ref/$referralCode',
        );
    }
  }

  /// Udělení odměny referrerovi
  Future<ReferralReward> _grantReferrerReward(String referrerId) async {
    final reward = ReferralReward(
      id: 'reward_${DateTime.now().millisecondsSinceEpoch}',
      type: RewardType.premiumDays,
      title: 'Premium přístup',
      description: '7 dní premium zdarma',
      value: 7.0,
      requiredReferrals: 1,
      claimedBy: [referrerId],
      createdAt: DateTime.now(),
    );

    // V produkci by se zde skutečně udělil premium přístup
    debugPrint('🎁 Udělena odměna referrerovi: ${reward.title}');

    return reward;
  }

  /// Udělení odměny novému uživateli
  Future<ReferralReward> _grantRefereeReward(String refereeId) async {
    final reward = ReferralReward(
      id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
      type: RewardType.premiumDays,
      title: 'Vítací bonus',
      description: '3 dny premium zdarma',
      value: 3.0,
      requiredReferrals: 0,
      claimedBy: [refereeId],
      createdAt: DateTime.now(),
    );

    debugPrint('🎁 Udělena vítací odměna: ${reward.title}');
    return reward;
  }

  /// Kontrola bonus odměn
  Future<ReferralReward?> _checkBonusRewards(String userId) async {
    final stats = await getUserReferralStats(userId);

    // Bonus za 5 referrals
    if (stats.successfulReferrals == 5) {
      return ReferralReward(
        id: 'bonus_5_${DateTime.now().millisecondsSinceEpoch}',
        type: RewardType.premiumDays,
        title: 'Milestone bonus!',
        description: '30 dní premium zdarma',
        value: 30.0,
        requiredReferrals: 5,
        claimedBy: [userId],
        createdAt: DateTime.now(),
      );
    }

    return null;
  }

  /// Aktualizace referral statistik
  Future<void> _updateReferralStats(String userId, bool successful) async {
    final currentStats =
        _userStats[userId] ??
        ReferralStats(
          userId: userId,
          totalReferrals: 0,
          successfulReferrals: 0,
          totalRewardsEarned: 0.0,
          currentStreak: 0,
          bestStreak: 0,
          tier: ReferralTier.bronze,
        );

    final newStats = currentStats.copyWith(
      totalReferrals: currentStats.totalReferrals + 1,
      successfulReferrals: successful
          ? currentStats.successfulReferrals + 1
          : currentStats.successfulReferrals,
      currentStreak: successful ? currentStats.currentStreak + 1 : 0,
      bestStreak: successful
          ? max(currentStats.bestStreak, currentStats.currentStreak + 1)
          : currentStats.bestStreak,
      tier: _calculateTier(
        successful
            ? currentStats.successfulReferrals + 1
            : currentStats.successfulReferrals,
      ),
    );

    _userStats[userId] = newStats;
  }

  /// Výpočet tier podle počtu referrals
  ReferralTier _calculateTier(int referrals) {
    if (referrals >= 50) return ReferralTier.diamond;
    if (referrals >= 25) return ReferralTier.platinum;
    if (referrals >= 10) return ReferralTier.gold;
    if (referrals >= 5) return ReferralTier.silver;
    return ReferralTier.bronze;
  }

  /// Sdílení na konkrétní platformy
  Future<void> _shareToWhatsApp(ShareContent content) async {
    // WhatsApp sharing
    await Share.share(content.message);
  }

  Future<void> _shareToInstagram(ShareContent content) async {
    // Instagram sharing
    await Share.share(content.message);
  }

  Future<void> _shareViaSMS(ShareContent content) async {
    // SMS sharing
    await Share.share(content.message);
  }

  Future<void> _shareViaEmail(ShareContent content) async {
    // Email sharing
    await Share.share(
      content.message,
      subject: 'Pridruži mi se u Croatia Travel App! 🇭🇷',
    );
  }

  /// Setup výchozích odměn
  Future<void> _setupDefaultRewards() async {
    if (_rewards.isNotEmpty) return;

    _rewards.addAll([
      ReferralReward(
        id: 'reward_1',
        type: RewardType.premiumDays,
        title: '1. referral bonus',
        description: '7 dní premium zdarma',
        value: 7.0,
        requiredReferrals: 1,
        claimedBy: [],
        createdAt: DateTime.now(),
      ),
      ReferralReward(
        id: 'reward_5',
        type: RewardType.premiumDays,
        title: '5 referrals milestone',
        description: '30 dní premium zdarma',
        value: 30.0,
        requiredReferrals: 5,
        claimedBy: [],
        createdAt: DateTime.now(),
      ),
      ReferralReward(
        id: 'reward_10',
        type: RewardType.premiumDays,
        title: '10 referrals milestone',
        description: '90 dní premium zdarma',
        value: 90.0,
        requiredReferrals: 10,
        claimedBy: [],
        createdAt: DateTime.now(),
      ),
    ]);
  }

  /// Načítání a ukládání dat
  Future<void> _loadReferralData() async {
    // Load from storage
  }

  Future<void> _saveReferralData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'referral_codes',
        jsonEncode(_referralCodes.map((rc) => rc.toJson()).toList()),
      );
      await prefs.setString(
        'referral_rewards',
        jsonEncode(_rewards.map((r) => r.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání referral dat: $e');
    }
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<ReferralCode> get referralCodes => List.unmodifiable(_referralCodes);
  List<ReferralReward> get rewards => List.unmodifiable(_rewards);
}

/// Modely pro referral program
class ReferralCode {
  final String id;
  final String userId;
  final String code;
  final DateTime createdAt;
  final bool isActive;
  final int usageCount;
  final int maxUsage;
  final DateTime? lastUsedAt;

  ReferralCode({
    required this.id,
    required this.userId,
    required this.code,
    required this.createdAt,
    required this.isActive,
    required this.usageCount,
    required this.maxUsage,
    this.lastUsedAt,
  });

  ReferralCode copyWith({
    String? id,
    String? userId,
    String? code,
    DateTime? createdAt,
    bool? isActive,
    int? usageCount,
    int? maxUsage,
    DateTime? lastUsedAt,
  }) {
    return ReferralCode(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      code: code ?? this.code,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      usageCount: usageCount ?? this.usageCount,
      maxUsage: maxUsage ?? this.maxUsage,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'userId': userId,
    'code': code,
    'createdAt': createdAt.toIso8601String(),
    'isActive': isActive,
    'usageCount': usageCount,
    'maxUsage': maxUsage,
    'lastUsedAt': lastUsedAt?.toIso8601String(),
  };
}

class ReferralReward {
  final String id;
  final RewardType type;
  final String title;
  final String description;
  final double value;
  final int requiredReferrals;
  final List<String> claimedBy;
  final DateTime createdAt;

  ReferralReward({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.value,
    required this.requiredReferrals,
    required this.claimedBy,
    required this.createdAt,
  });

  ReferralReward copyWith({
    String? id,
    RewardType? type,
    String? title,
    String? description,
    double? value,
    int? requiredReferrals,
    List<String>? claimedBy,
    DateTime? createdAt,
  }) {
    return ReferralReward(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      value: value ?? this.value,
      requiredReferrals: requiredReferrals ?? this.requiredReferrals,
      claimedBy: claimedBy ?? this.claimedBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'title': title,
    'description': description,
    'value': value,
    'requiredReferrals': requiredReferrals,
    'claimedBy': claimedBy,
    'createdAt': createdAt.toIso8601String(),
  };
}

class ReferralStats {
  final String userId;
  final int totalReferrals;
  final int successfulReferrals;
  final double totalRewardsEarned;
  final int currentStreak;
  final int bestStreak;
  final ReferralTier tier;

  ReferralStats({
    required this.userId,
    required this.totalReferrals,
    required this.successfulReferrals,
    required this.totalRewardsEarned,
    required this.currentStreak,
    required this.bestStreak,
    required this.tier,
  });

  ReferralStats copyWith({
    String? userId,
    int? totalReferrals,
    int? successfulReferrals,
    double? totalRewardsEarned,
    int? currentStreak,
    int? bestStreak,
    ReferralTier? tier,
  }) {
    return ReferralStats(
      userId: userId ?? this.userId,
      totalReferrals: totalReferrals ?? this.totalReferrals,
      successfulReferrals: successfulReferrals ?? this.successfulReferrals,
      totalRewardsEarned: totalRewardsEarned ?? this.totalRewardsEarned,
      currentStreak: currentStreak ?? this.currentStreak,
      bestStreak: bestStreak ?? this.bestStreak,
      tier: tier ?? this.tier,
    );
  }
}

class ReferralResult {
  final bool success;
  final String? referrerId;
  final String? refereeId;
  final ReferralReward? referrerReward;
  final ReferralReward? refereeReward;
  final ReferralReward? bonusReward;
  final String? error;

  ReferralResult({
    required this.success,
    this.referrerId,
    this.refereeId,
    this.referrerReward,
    this.refereeReward,
    this.bonusReward,
    this.error,
  });
}

class ReferralLeaderboardEntry {
  final String userId;
  final String userName;
  final int referralCount;
  final double rewardsEarned;
  final ReferralTier tier;
  final String avatar;

  ReferralLeaderboardEntry({
    required this.userId,
    required this.userName,
    required this.referralCount,
    required this.rewardsEarned,
    required this.tier,
    required this.avatar,
  });
}

class ShareContent {
  final String message;
  final String deepLink;
  final List<String>? hashtags;

  ShareContent({required this.message, required this.deepLink, this.hashtags});
}

class ReferralEvent {
  final ReferralEventType type;
  final String userId;
  final String? refereeId;
  final String? referralCode;
  final String? rewardId;
  final ReferralReward? reward;
  final ShareMethod? shareMethod;
  final DateTime timestamp;

  ReferralEvent({
    required this.type,
    required this.userId,
    this.refereeId,
    this.referralCode,
    this.rewardId,
    this.reward,
    this.shareMethod,
    required this.timestamp,
  });
}

enum ShareMethod { general, whatsapp, instagram, sms, email }

enum RewardType { premiumDays, credits, discount, merchandise }

enum ReferralTier { bronze, silver, gold, platinum, diamond }

enum LeaderboardType { allTime, monthly, weekly }

enum ReferralEventType {
  codeCreated,
  codeShared,
  codeUsed,
  rewardClaimed,
  tierUpgraded,
}
