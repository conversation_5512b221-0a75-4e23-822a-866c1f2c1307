import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/watercolor_theme.dart';

/// Watercolor pozadí s ručně malovaným efektem
class WatercolorBackground extends StatelessWidget {
  final Widget child;
  final List<Color>? colors;
  final bool animated;
  final Duration animationDuration;

  const WatercolorBackground({
    super.key,
    required this.child,
    this.colors,
    this.animated = false,
    this.animationDuration = const Duration(seconds: 10),
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColors = colors ?? WatercolorTheme.paperGradient;

    if (animated) {
      return AnimatedWatercolorBackground(
        colors: backgroundColors,
        duration: animationDuration,
        child: child,
      );
    }

    return Container(
      decoration: BoxDecoration(
        gradient: WatercolorTheme.createWatercolorGradient(backgroundColors),
      ),
      child: CustomPaint(
        painter: WatercolorBackgroundPainter(colors: backgroundColors),
        child: child,
      ),
    );
  }
}

/// Animované watercolor pozadí
class AnimatedWatercolorBackground extends StatefulWidget {
  final Widget child;
  final List<Color> colors;
  final Duration duration;

  const AnimatedWatercolorBackground({
    super.key,
    required this.child,
    required this.colors,
    required this.duration,
  });

  @override
  State<AnimatedWatercolorBackground> createState() => _AnimatedWatercolorBackgroundState();
}

class _AnimatedWatercolorBackgroundState extends State<AnimatedWatercolorBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.linear),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: WatercolorTheme.createWatercolorGradient(widget.colors),
          ),
          child: CustomPaint(
            painter: AnimatedWatercolorBackgroundPainter(
              colors: widget.colors,
              animationValue: _animation.value,
            ),
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// Painter pro statické watercolor pozadí
class WatercolorBackgroundPainter extends CustomPainter {
  final List<Color> colors;

  WatercolorBackgroundPainter({required this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    _paintWatercolorTexture(canvas, size);
  }

  void _paintWatercolorTexture(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    // Vytvoří několik vrstev watercolor efektu
    final layers = [
      (colors[0].withValues(alpha: 0.05), 0.8),
      (colors.length > 1 ? colors[1].withValues(alpha: 0.03) : colors[0].withValues(alpha: 0.03), 0.6),
      (colors.length > 2 ? colors[2].withValues(alpha: 0.02) : colors[0].withValues(alpha: 0.02), 0.4),
    ];

    for (int layerIndex = 0; layerIndex < layers.length; layerIndex++) {
      final (color, intensity) = layers[layerIndex];
      paint.color = color;

      // Vytvoří několik nepravidelných skvrn
      for (int i = 0; i < 8; i++) {
        final center = Offset(
          (i * 0.3 + 0.1) * size.width + math.sin(i * 0.7) * size.width * 0.2,
          (i * 0.2 + 0.1) * size.height + math.cos(i * 0.5) * size.height * 0.3,
        );

        final radius = size.width * intensity * (0.3 + math.sin(i * 1.2) * 0.2);
        
        _paintWatercolorBlob(canvas, center, radius, paint, i);
      }
    }
  }

  void _paintWatercolorBlob(Canvas canvas, Offset center, double radius, Paint paint, int seed) {
    final path = Path();
    final points = <Offset>[];
    
    // Vytvoří nepravidelný tvar
    for (int i = 0; i < 16; i++) {
      final angle = (i * 22.5) * math.pi / 180;
      final variation = 0.6 + math.sin(i * 0.8 + seed) * 0.4;
      final r = radius * variation;
      
      points.add(Offset(
        center.dx + r * math.cos(angle),
        center.dy + r * math.sin(angle),
      ));
    }
    
    if (points.isNotEmpty) {
      path.moveTo(points[0].dx, points[0].dy);
      
      for (int i = 1; i < points.length; i++) {
        final current = points[i];
        final next = points[(i + 1) % points.length];
        
        final cp1 = Offset(
          current.dx + (next.dx - current.dx) * 0.3,
          current.dy + (next.dy - current.dy) * 0.3,
        );
        
        path.quadraticBezierTo(current.dx, current.dy, cp1.dx, cp1.dy);
      }
      
      path.close();
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Painter pro animované watercolor pozadí
class AnimatedWatercolorBackgroundPainter extends CustomPainter {
  final List<Color> colors;
  final double animationValue;

  AnimatedWatercolorBackgroundPainter({
    required this.colors,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    _paintAnimatedWatercolorTexture(canvas, size);
  }

  void _paintAnimatedWatercolorTexture(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    // Animované vrstvy
    final layers = [
      (colors[0].withValues(alpha: 0.06), 0.9),
      (colors.length > 1 ? colors[1].withValues(alpha: 0.04) : colors[0].withValues(alpha: 0.04), 0.7),
      (colors.length > 2 ? colors[2].withValues(alpha: 0.03) : colors[0].withValues(alpha: 0.03), 0.5),
    ];

    for (int layerIndex = 0; layerIndex < layers.length; layerIndex++) {
      final (color, intensity) = layers[layerIndex];
      paint.color = color;

      // Animované skvrny
      for (int i = 0; i < 6; i++) {
        final animOffset = animationValue * 2 * math.pi + i * 0.5;
        
        final center = Offset(
          (i * 0.4 + 0.1) * size.width + math.sin(animOffset) * size.width * 0.1,
          (i * 0.3 + 0.1) * size.height + math.cos(animOffset * 0.7) * size.height * 0.15,
        );

        final radius = size.width * intensity * (0.25 + math.sin(animOffset * 0.3) * 0.15);
        
        _paintAnimatedWatercolorBlob(canvas, center, radius, paint, i, animationValue);
      }
    }
  }

  void _paintAnimatedWatercolorBlob(
    Canvas canvas, 
    Offset center, 
    double radius, 
    Paint paint, 
    int seed, 
    double animValue,
  ) {
    final path = Path();
    final points = <Offset>[];
    
    // Animovaný nepravidelný tvar
    for (int i = 0; i < 12; i++) {
      final angle = (i * 30) * math.pi / 180;
      final animatedVariation = 0.7 + math.sin(i * 0.6 + seed + animValue * 2 * math.pi) * 0.3;
      final r = radius * animatedVariation;
      
      points.add(Offset(
        center.dx + r * math.cos(angle),
        center.dy + r * math.sin(angle),
      ));
    }
    
    if (points.isNotEmpty) {
      path.moveTo(points[0].dx, points[0].dy);
      
      for (int i = 1; i < points.length; i++) {
        final current = points[i];
        final next = points[(i + 1) % points.length];
        
        final cp1 = Offset(
          current.dx + (next.dx - current.dx) * 0.4,
          current.dy + (next.dy - current.dy) * 0.4,
        );
        
        path.quadraticBezierTo(current.dx, current.dy, cp1.dx, cp1.dy);
      }
      
      path.close();
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is AnimatedWatercolorBackgroundPainter &&
           oldDelegate.animationValue != animationValue;
  }
}

/// Watercolor card s ručně malovaným efektem
class WatercolorCard extends StatelessWidget {
  final Widget child;
  final List<Color>? colors;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final VoidCallback? onTap;

  const WatercolorCard({
    super.key,
    required this.child,
    this.colors,
    this.padding,
    this.margin,
    this.elevation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardColors = colors ?? [Colors.white, WatercolorTheme.creamPaper];

    Widget card = Container(
      margin: margin,
      decoration: WatercolorTheme.watercolorCardDecoration(
        gradient: cardColors,
        shadows: elevation != null 
          ? [BoxShadow(
              color: WatercolorTheme.adriaticBlue.withValues(alpha: 0.1),
              blurRadius: elevation! * 2,
              offset: Offset(0, elevation!),
            )]
          : WatercolorTheme.softWatercolorShadow,
      ),
      child: CustomPaint(
        painter: WatercolorCardPainter(colors: cardColors),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(WatercolorTheme.spacingM),
          child: child,
        ),
      ),
    );

    if (onTap != null) {
      card = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: WatercolorTheme.softRadius,
          child: card,
        ),
      );
    }

    return card;
  }
}

/// Painter pro watercolor karty
class WatercolorCardPainter extends CustomPainter {
  final List<Color> colors;

  WatercolorCardPainter({required this.colors});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1);

    // Jemné watercolor okraje
    final edgeColor = colors[0].withValues(alpha: 0.1);
    paint.color = edgeColor;

    // Okrajové efekty
    final edgePath = Path();
    edgePath.addRRect(RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      const Radius.circular(12),
    ));

    canvas.drawPath(edgePath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
