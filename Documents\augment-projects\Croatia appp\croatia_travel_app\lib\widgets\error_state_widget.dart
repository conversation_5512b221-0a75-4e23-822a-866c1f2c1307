import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Widget pro zobrazení error stavů s možností retry
class ErrorStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final String? retryButtonText;

  const ErrorStateWidget({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.icon,
    this.retryButtonText,
  });

  // Přednastavené error stavy
  factory ErrorStateWidget.network({
    VoidCallback? onRetry,
  }) {
    return ErrorStateWidget(
      title: 'Problém s připojením',
      message: 'Zkontrolujte internetové připojení a zkuste to znovu.',
      icon: Icons.wifi_off,
      onRetry: onRetry,
      retryButtonText: 'Zkusit znovu',
    );
  }

  factory ErrorStateWidget.serverError({
    VoidCallback? onRetry,
  }) {
    return ErrorStateWidget(
      title: 'Chyba serveru',
      message: 'Něco se pokazilo na našem serveru. Zkuste to za chvíli.',
      icon: Icons.error_outline,
      onRetry: onRetry,
      retryButtonText: 'Zkusit znovu',
    );
  }

  factory ErrorStateWidget.noData({
    VoidCallback? onRetry,
  }) {
    return ErrorStateWidget(
      title: 'Žádná data',
      message: 'Nepodařilo se načíst data. Zkuste to znovu.',
      icon: Icons.inbox_outlined,
      onRetry: onRetry,
      retryButtonText: 'Obnovit',
    );
  }

  factory ErrorStateWidget.locationPermission({
    VoidCallback? onRetry,
  }) {
    return ErrorStateWidget(
      title: 'Povolení k poloze',
      message: 'Pro lepší zážitek povolte přístup k vaší poloze v nastavení.',
      icon: Icons.location_off,
      onRetry: onRetry,
      retryButtonText: 'Otevřít nastavení',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Ikona s watercolor efektem
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF006994).withValues(alpha: 0.1),
                  const Color(0xFF2E8B8B).withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Icon(
              icon ?? Icons.error_outline,
              size: 64,
              color: const Color(0xFF006994),
            ),
          ),

          const SizedBox(height: 24),

          // Titulek
          Text(
            title,
            style: GoogleFonts.playfairDisplay(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2C2C2C),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // Zpráva
          Text(
            message,
            style: GoogleFonts.inter(
              fontSize: 16,
              color: const Color(0xFF666666),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Retry tlačítko
          if (onRetry != null)
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF006994),
                    const Color(0xFF2E8B8B),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF006994).withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onRetry,
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.refresh,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          retryButtonText ?? 'Zkusit znovu',
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Kompaktní error widget pro menší prostory
class CompactErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const CompactErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF3E0),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFF6B35).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: const Color(0xFFFF6B35),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: const Color(0xFF2C2C2C),
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 12),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: const Color(0xFFFF6B35),
                padding: const EdgeInsets.symmetric(horizontal: 12),
              ),
              child: Text(
                'Zkusit znovu',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Snackbar pro rychlé error zprávy
class ErrorSnackBar {
  static void show(
    BuildContext context,
    String message, {
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFFDC143C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: onRetry != null
            ? SnackBarAction(
                label: 'Zkusit znovu',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        duration: duration,
      ),
    );
  }
}
