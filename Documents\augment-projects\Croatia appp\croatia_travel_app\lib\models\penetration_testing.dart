import 'package:json_annotation/json_annotation.dart';

part 'penetration_testing.g.dart';

/// 🔒 PENETRATION TESTING MODELS - Modely pro penetrační testování

/// Typ bezpečnostní události
enum SecurityEventType {
  testStarted,
  testCompleted,
  vulnerabilityFound,
  auditCompleted,
  scanInitiated,
}

/// Kategorie testu
enum TestCategory {
  injection,
  authentication,
  cryptography,
  authorization,
  dataValidation,
  sessionManagement,
  configuration,
}

/// Závažnost testu
enum TestSeverity {
  low,
  medium,
  high,
  critical,
}

/// Závažnost vulnerability
enum VulnerabilitySeverity {
  info,
  low,
  medium,
  high,
  critical,
}

/// Kategorie vulnerability
enum VulnerabilityCategory {
  injection,
  authentication,
  cryptography,
  authorization,
  dataExposure,
  configuration,
  businessLogic,
}

/// Bezpečnostní test
@JsonSerializable()
class SecurityTest {
  final String id;
  final String name;
  final String description;
  final TestCategory category;
  final TestSeverity severity;
  final Duration estimatedDuration;
  final List<String> requirements;

  const SecurityTest({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.severity,
    required this.estimatedDuration,
    required this.requirements,
  });

  factory SecurityTest.fromJson(Map<String, dynamic> json) =>
      _$SecurityTestFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityTestToJson(this);
}

/// Vulnerability
@JsonSerializable()
class Vulnerability {
  final String id;
  final String title;
  final String description;
  final VulnerabilitySeverity severity;
  final VulnerabilityCategory category;
  final String cwe;
  final String owasp;
  final String affectedComponent;
  final String testCase;
  final String recommendation;
  final List<String> references;

  const Vulnerability({
    required this.id,
    required this.title,
    required this.description,
    required this.severity,
    required this.category,
    required this.cwe,
    required this.owasp,
    required this.affectedComponent,
    required this.testCase,
    required this.recommendation,
    required this.references,
  });

  factory Vulnerability.fromJson(Map<String, dynamic> json) =>
      _$VulnerabilityFromJson(json);
  Map<String, dynamic> toJson() => _$VulnerabilityToJson(this);
}

/// Výsledek testu
@JsonSerializable()
class TestResult {
  final String testId;
  final String testName;
  final bool passed;
  final List<Vulnerability> vulnerabilities;
  final Duration executionTime;
  final String details;

  const TestResult({
    required this.testId,
    required this.testName,
    required this.passed,
    required this.vulnerabilities,
    required this.executionTime,
    required this.details,
  });

  factory TestResult.fromJson(Map<String, dynamic> json) =>
      _$TestResultFromJson(json);
  Map<String, dynamic> toJson() => _$TestResultToJson(this);
}

/// Zpráva z bezpečnostního auditu
@JsonSerializable()
class SecurityAuditReport {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final Duration duration;
  final List<TestResult> testResults;
  final int criticalVulnerabilities;
  final int highVulnerabilities;
  final int mediumVulnerabilities;
  final int lowVulnerabilities;
  final double securityScore;
  final List<String> recommendations;
  final Map<String, bool> complianceStatus;

  const SecurityAuditReport({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.testResults,
    required this.criticalVulnerabilities,
    required this.highVulnerabilities,
    required this.mediumVulnerabilities,
    required this.lowVulnerabilities,
    required this.securityScore,
    required this.recommendations,
    required this.complianceStatus,
  });

  factory SecurityAuditReport.fromJson(Map<String, dynamic> json) =>
      _$SecurityAuditReportFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityAuditReportToJson(this);
}

/// Bezpečnostní událost
@JsonSerializable()
class SecurityEvent {
  final SecurityEventType type;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const SecurityEvent({
    required this.type,
    required this.timestamp,
    required this.data,
  });

  factory SecurityEvent.fromJson(Map<String, dynamic> json) =>
      _$SecurityEventFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityEventToJson(this);
}

/// Bezpečnostní konfigurace
@JsonSerializable()
class SecurityConfiguration {
  final bool enableAutomaticScanning;
  final Duration scanInterval;
  final List<TestCategory> enabledTestCategories;
  final TestSeverity minimumSeverityLevel;
  final bool generateDetailedReports;

  const SecurityConfiguration({
    required this.enableAutomaticScanning,
    required this.scanInterval,
    required this.enabledTestCategories,
    required this.minimumSeverityLevel,
    required this.generateDetailedReports,
  });

  factory SecurityConfiguration.fromJson(Map<String, dynamic> json) =>
      _$SecurityConfigurationFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityConfigurationToJson(this);
}

/// Bezpečnostní metrika
@JsonSerializable()
class SecurityMetrics {
  final DateTime reportDate;
  final int totalTestsRun;
  final int vulnerabilitiesFound;
  final int vulnerabilitiesFixed;
  final double averageSecurityScore;
  final Map<VulnerabilitySeverity, int> vulnerabilityBreakdown;
  final List<String> topVulnerabilityTypes;

  const SecurityMetrics({
    required this.reportDate,
    required this.totalTestsRun,
    required this.vulnerabilitiesFound,
    required this.vulnerabilitiesFixed,
    required this.averageSecurityScore,
    required this.vulnerabilityBreakdown,
    required this.topVulnerabilityTypes,
  });

  factory SecurityMetrics.fromJson(Map<String, dynamic> json) =>
      _$SecurityMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityMetricsToJson(this);
}
