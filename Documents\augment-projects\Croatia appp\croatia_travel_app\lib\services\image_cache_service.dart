import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Service pro cachování obrázků pro lepší performance
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int _maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 7 dní v ms

  Directory? _cacheDirectory;
  final Map<String, DateTime> _cacheMetadata = {};

  /// Inicializace cache service
  Future<void> initialize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/image_cache');
      
      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }

      await _loadCacheMetadata();
      await _cleanupOldCache();
    } catch (e) {
      debugPrint('Chyba při inicializaci image cache: $e');
    }
  }

  /// Načtení metadat cache
  Future<void> _loadCacheMetadata() async {
    try {
      final metadataFile = File('${_cacheDirectory!.path}/metadata.json');
      if (await metadataFile.exists()) {
        final content = await metadataFile.readAsString();
        final Map<String, dynamic> data = json.decode(content);
        
        data.forEach((key, value) {
          _cacheMetadata[key] = DateTime.fromMillisecondsSinceEpoch(value);
        });
      }
    } catch (e) {
      debugPrint('Chyba při načítání cache metadat: $e');
    }
  }

  /// Uložení metadat cache
  Future<void> _saveCacheMetadata() async {
    try {
      final metadataFile = File('${_cacheDirectory!.path}/metadata.json');
      final Map<String, int> data = {};
      
      _cacheMetadata.forEach((key, value) {
        data[key] = value.millisecondsSinceEpoch;
      });

      await metadataFile.writeAsString(json.encode(data));
    } catch (e) {
      debugPrint('Chyba při ukládání cache metadat: $e');
    }
  }

  /// Vyčištění staré cache
  Future<void> _cleanupOldCache() async {
    try {
      final now = DateTime.now();
      final expiredKeys = <String>[];

      _cacheMetadata.forEach((key, timestamp) {
        if (now.difference(timestamp).inMilliseconds > _maxCacheAge) {
          expiredKeys.add(key);
        }
      });

      for (final key in expiredKeys) {
        await _removeCachedFile(key);
      }

      await _checkCacheSize();
    } catch (e) {
      debugPrint('Chyba při čištění cache: $e');
    }
  }

  /// Kontrola velikosti cache
  Future<void> _checkCacheSize() async {
    try {
      int totalSize = 0;
      final files = await _cacheDirectory!.list().toList();
      
      for (final file in files) {
        if (file is File && file.path.endsWith('.cache')) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      if (totalSize > _maxCacheSize) {
        await _reduceCacheSize(totalSize);
      }
    } catch (e) {
      debugPrint('Chyba při kontrole velikosti cache: $e');
    }
  }

  /// Redukce velikosti cache
  Future<void> _reduceCacheSize(int currentSize) async {
    try {
      // Seřadit podle stáří (nejstarší první)
      final sortedEntries = _cacheMetadata.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      int removedSize = 0;
      final targetReduction = currentSize - (_maxCacheSize * 0.8).round();

      for (final entry in sortedEntries) {
        if (removedSize >= targetReduction) break;

        final file = File('${_cacheDirectory!.path}/${entry.key}.cache');
        if (await file.exists()) {
          final stat = await file.stat();
          removedSize += stat.size;
          await _removeCachedFile(entry.key);
        }
      }
    } catch (e) {
      debugPrint('Chyba při redukci cache: $e');
    }
  }

  /// Odstranění cached souboru
  Future<void> _removeCachedFile(String key) async {
    try {
      final file = File('${_cacheDirectory!.path}/$key.cache');
      if (await file.exists()) {
        await file.delete();
      }
      _cacheMetadata.remove(key);
      await _saveCacheMetadata();
    } catch (e) {
      debugPrint('Chyba při mazání cached souboru: $e');
    }
  }

  /// Generování cache klíče z URL
  String _generateCacheKey(String url) {
    final bytes = utf8.encode(url);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Uložení obrázku do cache
  Future<void> cacheImage(String url, Uint8List imageData) async {
    if (_cacheDirectory == null) return;

    try {
      final key = _generateCacheKey(url);
      final file = File('${_cacheDirectory!.path}/$key.cache');
      
      await file.writeAsBytes(imageData);
      _cacheMetadata[key] = DateTime.now();
      await _saveCacheMetadata();
    } catch (e) {
      debugPrint('Chyba při cachování obrázku: $e');
    }
  }

  /// Načtení obrázku z cache
  Future<Uint8List?> getCachedImage(String url) async {
    if (_cacheDirectory == null) return null;

    try {
      final key = _generateCacheKey(url);
      final file = File('${_cacheDirectory!.path}/$key.cache');
      
      if (await file.exists()) {
        // Zkontrolovat stáří
        final metadata = _cacheMetadata[key];
        if (metadata != null) {
          final age = DateTime.now().difference(metadata).inMilliseconds;
          if (age < _maxCacheAge) {
            return await file.readAsBytes();
          } else {
            // Starý soubor, smazat
            await _removeCachedFile(key);
          }
        }
      }
    } catch (e) {
      debugPrint('Chyba při načítání cached obrázku: $e');
    }

    return null;
  }

  /// Kontrola, zda je obrázek v cache
  Future<bool> isCached(String url) async {
    if (_cacheDirectory == null) return false;

    try {
      final key = _generateCacheKey(url);
      final file = File('${_cacheDirectory!.path}/$key.cache');
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Vyčištění celé cache
  Future<void> clearCache() async {
    if (_cacheDirectory == null) return;

    try {
      final files = await _cacheDirectory!.list().toList();
      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }
      _cacheMetadata.clear();
    } catch (e) {
      debugPrint('Chyba při čištění cache: $e');
    }
  }

  /// Získání velikosti cache
  Future<int> getCacheSize() async {
    if (_cacheDirectory == null) return 0;

    try {
      int totalSize = 0;
      final files = await _cacheDirectory!.list().toList();
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Formátování velikosti pro zobrazení
  String formatCacheSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
