import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cyber_insurance.dart';

/// 🛡️ CYBER LIABILITY INSURANCE SERVICE - Cyber security insurance management
class CyberLiabilityInsuranceService {
  static final CyberLiabilityInsuranceService _instance =
      CyberLiabilityInsuranceService._internal();
  factory CyberLiabilityInsuranceService() => _instance;
  CyberLiabilityInsuranceService._internal();

  bool _isInitialized = false;
  final List<InsurancePolicy> _policies = [];
  final List<InsuranceClaim> _claims = [];
  final List<RiskAssessment> _riskAssessments = [];
  final Map<String, CoverageStatus> _coverageStatus = {};
  final StreamController<InsuranceEvent> _eventController =
      StreamController.broadcast();

  /// Stream insurance událostí
  Stream<InsuranceEvent> get insuranceEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🛡️ Inicializuji Cyber Liability Insurance Service...');

      await _loadInsurancePolicies();
      await _loadRiskAssessments();
      await _initializeCoverageMonitoring();

      _isInitialized = true;
      debugPrint('✅ Cyber Liability Insurance Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Cyber Insurance: $e');
      await _createDefaultPolicies();
      _isInitialized = true;
    }
  }

  /// Provedení risk assessment pro cyber insurance
  Future<RiskAssessment> conductRiskAssessment() async {
    try {
      debugPrint('🔍 Provádím cyber risk assessment...');

      final assessmentId = 'risk_${DateTime.now().millisecondsSinceEpoch}';
      final startTime = DateTime.now();

      // Analýza různých rizikových faktorů
      final dataRisk = await _assessDataRisk();
      final systemRisk = await _assessSystemRisk();
      final complianceRisk = await _assessComplianceRisk();
      final businessRisk = await _assessBusinessRisk();
      final thirdPartyRisk = await _assessThirdPartyRisk();

      // Výpočet celkového risk score
      final overallRiskScore = _calculateOverallRiskScore([
        dataRisk,
        systemRisk,
        complianceRisk,
        businessRisk,
        thirdPartyRisk,
      ]);

      final riskLevel = _determineRiskLevel(overallRiskScore);

      final assessment = RiskAssessment(
        id: assessmentId,
        conductedAt: startTime,
        overallRiskScore: overallRiskScore,
        riskLevel: riskLevel,
        dataRisk: dataRisk,
        systemRisk: systemRisk,
        complianceRisk: complianceRisk,
        businessRisk: businessRisk,
        thirdPartyRisk: thirdPartyRisk,
        recommendations: _generateRiskRecommendations(overallRiskScore),
        estimatedAnnualLoss: _estimateAnnualLoss(overallRiskScore),
        nextAssessmentDue: DateTime.now().add(const Duration(days: 90)),
      );

      _riskAssessments.add(assessment);
      await _saveRiskAssessments();

      _eventController.add(
        InsuranceEvent(
          type: InsuranceEventType.riskAssessmentCompleted,
          assessmentId: assessmentId,
          timestamp: DateTime.now(),
          data: {
            'riskScore': overallRiskScore,
            'riskLevel': riskLevel.name,
            'estimatedLoss': assessment.estimatedAnnualLoss,
          },
        ),
      );

      debugPrint(
        '✅ Risk assessment dokončen: ${riskLevel.name} (${overallRiskScore.toStringAsFixed(1)})',
      );
      return assessment;
    } catch (e) {
      debugPrint('❌ Chyba při risk assessment: $e');
      rethrow;
    }
  }

  /// Získání insurance quote na základě risk assessment
  Future<InsuranceQuote> getInsuranceQuote({
    required String riskAssessmentId,
    required CoverageType coverageType,
    required double coverageLimit,
    required Duration policyTerm,
  }) async {
    try {
      final assessment = _riskAssessments.firstWhere(
        (a) => a.id == riskAssessmentId,
      );

      // Výpočet premium na základě rizika
      final basePremium = _calculateBasePremium(coverageLimit, policyTerm);
      final riskMultiplier = _getRiskMultiplier(assessment.riskLevel);
      final coverageMultiplier = _getCoverageMultiplier(coverageType);

      final annualPremium = basePremium * riskMultiplier * coverageMultiplier;
      final deductible = _calculateDeductible(
        coverageLimit,
        assessment.riskLevel,
      );

      final quote = InsuranceQuote(
        id: 'quote_${DateTime.now().millisecondsSinceEpoch}',
        riskAssessmentId: riskAssessmentId,
        coverageType: coverageType,
        coverageLimit: coverageLimit,
        annualPremium: annualPremium,
        deductible: deductible,
        policyTerm: policyTerm,
        validUntil: DateTime.now().add(const Duration(days: 30)),
        coverageDetails: _getCoverageDetails(coverageType),
        exclusions: _getExclusions(coverageType),
        riskFactors: [
          assessment.dataRisk,
          assessment.systemRisk,
          assessment.complianceRisk,
          assessment.businessRisk,
          assessment.thirdPartyRisk,
        ],
        discounts: _calculateDiscountsMap(assessment),
        createdAt: DateTime.now(),
      );

      _eventController.add(
        InsuranceEvent(
          type: InsuranceEventType.quoteGenerated,
          quoteId: quote.id,
          timestamp: DateTime.now(),
          data: {
            'coverageType': coverageType.name,
            'annualPremium': annualPremium,
            'coverageLimit': coverageLimit,
          },
        ),
      );

      debugPrint(
        '💰 Insurance quote vygenerován: ${annualPremium.toStringAsFixed(0)}€/rok',
      );
      return quote;
    } catch (e) {
      debugPrint('❌ Chyba při generování quote: $e');
      rethrow;
    }
  }

  /// Nákup insurance policy
  Future<InsurancePolicy> purchasePolicy({
    required String quoteId,
    required Map<String, dynamic> paymentDetails,
    required Map<String, dynamic> companyDetails,
  }) async {
    try {
      // V produkci by se zde provedla platba a komunikace s pojišťovnou

      final policyId = 'policy_${DateTime.now().millisecondsSinceEpoch}';
      final startDate = DateTime.now();

      final policy = InsurancePolicy(
        id: policyId,
        quoteId: quoteId,
        policyNumber:
            'CYB-${DateTime.now().year}-${Random().nextInt(10000).toString().padLeft(4, '0')}',
        insurer: 'CyberGuard Insurance Ltd.',
        coverageType: CoverageType.comprehensive, // Z quote
        coverageLimit: 1000000.0, // Z quote
        annualPremium: 15000.0, // Z quote
        deductible: 5000.0, // Z quote
        startDate: startDate,
        endDate: startDate.add(const Duration(days: 365)),
        status: PolicyStatus.active,
        companyDetails: companyDetails,
        coverageDetails: _getComprehensiveCoverageDetails(),
        exclusions: _getStandardExclusions(),
        claims: [],
        lastPremiumPaid: DateTime.now(),
        nextPremiumDue: DateTime.now().add(const Duration(days: 365)),
      );

      _policies.add(policy);
      await _savePolicies();

      _eventController.add(
        InsuranceEvent(
          type: InsuranceEventType.policyPurchased,
          policyId: policyId,
          timestamp: DateTime.now(),
          data: {
            'policyNumber': policy.policyNumber,
            'coverageLimit': policy.coverageLimit,
            'annualPremium': policy.annualPremium,
          },
        ),
      );

      debugPrint('🛡️ Insurance policy zakoupena: ${policy.policyNumber}');
      return policy;
    } catch (e) {
      debugPrint('❌ Chyba při nákupu policy: $e');
      rethrow;
    }
  }

  /// Podání insurance claim
  Future<InsuranceClaim> fileClaim({
    required String policyId,
    required ClaimType claimType,
    required String incidentDescription,
    required DateTime incidentDate,
    required double estimatedLoss,
    List<String>? supportingDocuments,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      final policy = _policies.firstWhere((p) => p.id == policyId);

      final claim = InsuranceClaim(
        id: 'claim_${DateTime.now().millisecondsSinceEpoch}',
        policyId: policyId,
        claimNumber:
            'CLM-${DateTime.now().year}-${Random().nextInt(10000).toString().padLeft(4, '0')}',
        claimType: claimType,
        incidentDescription: incidentDescription,
        incidentDate: incidentDate,
        estimatedLoss: estimatedLoss,
        status: ClaimStatus.submitted,
        filedAt: DateTime.now(),
        supportingDocuments: supportingDocuments ?? [],
        additionalInfo: additionalInfo ?? {},
        updates: [],
      );

      _claims.add(claim);

      // Přidání claim k policy
      final policyIndex = _policies.indexWhere((p) => p.id == policyId);
      _policies[policyIndex] = policy.copyWith(
        claims: [...policy.claims, claim.id],
      );

      await _saveClaims();
      await _savePolicies();

      _eventController.add(
        InsuranceEvent(
          type: InsuranceEventType.claimFiled,
          claimId: claim.id,
          policyId: policyId,
          timestamp: DateTime.now(),
          data: {
            'claimNumber': claim.claimNumber,
            'claimType': claimType.name,
            'estimatedLoss': estimatedLoss,
          },
        ),
      );

      debugPrint('📋 Insurance claim podán: ${claim.claimNumber}');
      return claim;
    } catch (e) {
      debugPrint('❌ Chyba při podávání claim: $e');
      rethrow;
    }
  }

  /// Monitoring coverage status
  Future<CoverageReport> getCoverageReport() async {
    final activePolicies = _policies
        .where((p) => p.status == PolicyStatus.active)
        .toList();
    final totalCoverage = activePolicies.fold<double>(
      0,
      (sum, p) => sum + p.coverageLimit,
    );
    final totalPremiums = activePolicies.fold<double>(
      0,
      (sum, p) => sum + p.annualPremium,
    );

    final openClaims = _claims
        .where(
          (c) =>
              c.status == ClaimStatus.submitted ||
              c.status == ClaimStatus.underReview,
        )
        .toList();

    final recentAssessment = _riskAssessments.isNotEmpty
        ? _riskAssessments.last
        : null;

    return CoverageReport(
      reportDate: DateTime.now(),
      activePolicies: activePolicies.length,
      totalCoverageLimit: totalCoverage,
      totalAnnualPremiums: totalPremiums,
      openClaims: openClaims.length,
      currentRiskLevel: recentAssessment?.riskLevel ?? RiskLevel.unknown,
      coverageGaps: _identifyCoverageGaps(),
      recommendations: _generateCoverageRecommendations(),
      nextRenewalDate: _getNextRenewalDate(),
      complianceStatus: _checkInsuranceCompliance(),
    );
  }

  /// Risk assessment metody
  Future<RiskFactor> _assessDataRisk() async {
    // Analýza rizik spojených s daty
    var score = 0.0;
    final factors = <String>[];

    // Typ dat
    if (true) {
      // Osobní údaje
      score += 30;
      factors.add('Personal data processing');
    }

    // Objem dat
    score += 20; // Střední objem
    factors.add('Medium data volume');

    // Šifrování
    if (true) {
      // Implementováno
      score -= 15;
      factors.add('Data encryption implemented');
    }

    // GDPR compliance
    if (true) {
      // Compliant
      score -= 10;
      factors.add('GDPR compliant');
    }

    return RiskFactor(
      category: 'Data Risk',
      score: score.clamp(0, 100),
      factors: factors,
      impact: score > 50 ? RiskImpact.high : RiskImpact.medium,
    );
  }

  Future<RiskFactor> _assessSystemRisk() async {
    var score = 0.0;
    final factors = <String>[];

    // Cloud infrastructure
    score += 25;
    factors.add('Cloud-based infrastructure');

    // Security measures
    if (true) {
      // Implementováno
      score -= 20;
      factors.add('Strong security controls');
    }

    // Monitoring
    if (true) {
      // Implementováno
      score -= 10;
      factors.add('24/7 monitoring');
    }

    return RiskFactor(
      category: 'System Risk',
      score: score.clamp(0, 100),
      factors: factors,
      impact: score > 40 ? RiskImpact.high : RiskImpact.medium,
    );
  }

  Future<RiskFactor> _assessComplianceRisk() async {
    var score = 0.0;
    final factors = <String>[];

    // GDPR compliance
    final isGDPRCompliant =
        true; // V produkci by se kontrolovalo skutečné compliance
    if (isGDPRCompliant) {
      // Compliant
      score -= 20;
      factors.add('GDPR compliant');
    } else {
      score += 40;
      factors.add('GDPR non-compliance risk');
    }

    // SOC 2
    if (true) {
      // V procesu
      score -= 10;
      factors.add('SOC 2 certification in progress');
    }

    return RiskFactor(
      category: 'Compliance Risk',
      score: score.clamp(0, 100),
      factors: factors,
      impact: score > 30 ? RiskImpact.high : RiskImpact.low,
    );
  }

  Future<RiskFactor> _assessBusinessRisk() async {
    var score = 0.0;
    final factors = <String>[];

    // Revenue dependency
    score += 30;
    factors.add('High revenue dependency on digital services');

    // Business continuity
    if (true) {
      // Plán existuje
      score -= 15;
      factors.add('Business continuity plan exists');
    }

    return RiskFactor(
      category: 'Business Risk',
      score: score.clamp(0, 100),
      factors: factors,
      impact: score > 35 ? RiskImpact.high : RiskImpact.medium,
    );
  }

  Future<RiskFactor> _assessThirdPartyRisk() async {
    var score = 0.0;
    final factors = <String>[];

    // Počet third parties
    score += 25; // Střední počet
    factors.add('Multiple third-party integrations');

    // Due diligence
    if (true) {
      // Provedeno
      score -= 10;
      factors.add('Third-party due diligence completed');
    }

    return RiskFactor(
      category: 'Third Party Risk',
      score: score.clamp(0, 100),
      factors: factors,
      impact: score > 30 ? RiskImpact.medium : RiskImpact.low,
    );
  }

  /// Výpočetní metody
  double _calculateOverallRiskScore(List<RiskFactor> factors) {
    if (factors.isEmpty) return 0.0;

    // Vážený průměr podle impact
    var totalWeightedScore = 0.0;
    var totalWeight = 0.0;

    for (final factor in factors) {
      final weight = _getImpactWeight(factor.impact);
      totalWeightedScore += factor.score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
  }

  double _getImpactWeight(RiskImpact impact) {
    switch (impact) {
      case RiskImpact.critical:
        return 3.0;
      case RiskImpact.high:
        return 2.0;
      case RiskImpact.medium:
        return 1.5;
      case RiskImpact.low:
        return 1.0;
    }
  }

  RiskLevel _determineRiskLevel(double riskScore) {
    if (riskScore >= 70) return RiskLevel.critical;
    if (riskScore >= 50) return RiskLevel.high;
    if (riskScore >= 30) return RiskLevel.medium;
    return RiskLevel.low;
  }

  double _calculateBasePremium(double coverageLimit, Duration policyTerm) {
    // Základní premium: 1.5% z coverage limit ročně
    final annualRate = 0.015;
    final annualPremium = coverageLimit * annualRate;

    // Úprava podle délky policy
    final yearFraction = policyTerm.inDays / 365.0;
    return annualPremium * yearFraction;
  }

  double _getRiskMultiplier(RiskLevel riskLevel) {
    switch (riskLevel) {
      case RiskLevel.critical:
        return 3.0;
      case RiskLevel.high:
        return 2.0;
      case RiskLevel.medium:
        return 1.5;
      case RiskLevel.low:
        return 1.2;
      case RiskLevel.unknown:
        return 2.5; // Conservative approach
    }
  }

  double _getCoverageMultiplier(CoverageType coverageType) {
    switch (coverageType) {
      case CoverageType.basic:
        return 1.0;
      case CoverageType.standard:
        return 1.3;
      case CoverageType.comprehensive:
        return 1.8;
      case CoverageType.enterprise:
        return 2.5;
    }
  }

  double _calculateDeductible(double coverageLimit, RiskLevel riskLevel) {
    // Deductible jako procento z coverage limit
    var deductibleRate = 0.005; // 0.5% base

    switch (riskLevel) {
      case RiskLevel.critical:
        deductibleRate = 0.02; // 2%
        break;
      case RiskLevel.high:
        deductibleRate = 0.015; // 1.5%
        break;
      case RiskLevel.medium:
        deductibleRate = 0.01; // 1%
        break;
      case RiskLevel.low:
        deductibleRate = 0.005; // 0.5%
        break;
      case RiskLevel.unknown:
        deductibleRate = 0.02; // 2% conservative
        break;
    }

    return coverageLimit * deductibleRate;
  }

  List<String> _generateRiskRecommendations(double riskScore) {
    final recommendations = <String>[];

    if (riskScore > 60) {
      recommendations.addAll([
        'Implementujte advanced threat detection',
        'Proveďte penetration testing',
        'Zvyšte security awareness training',
        'Implementujte zero-trust architecture',
      ]);
    } else if (riskScore > 40) {
      recommendations.addAll([
        'Zlepšete incident response procedures',
        'Implementujte automated backup testing',
        'Proveďte security audit',
      ]);
    } else {
      recommendations.addAll([
        'Udržujte current security measures',
        'Regular security reviews',
        'Continue compliance monitoring',
      ]);
    }

    return recommendations;
  }

  double _estimateAnnualLoss(double riskScore) {
    // Odhad ročních ztrát na základě risk score
    final baseLoss = 50000.0; // 50k EUR base
    final riskMultiplier = riskScore / 100.0;

    return baseLoss * (1 + riskMultiplier * 3);
  }

  Map<String, dynamic> _getCoverageDetails(CoverageType coverageType) {
    switch (coverageType) {
      case CoverageType.basic:
        return {
          'dataBreachResponse': true,
          'businessInterruption': false,
          'cyberExtortion': false,
          'thirdPartyLiability': true,
          'regulatoryFines': false,
        };
      case CoverageType.comprehensive:
        return {
          'dataBreachResponse': true,
          'businessInterruption': true,
          'cyberExtortion': true,
          'thirdPartyLiability': true,
          'regulatoryFines': true,
          'reputationManagement': true,
          'forensicInvestigation': true,
        };
      default:
        return {};
    }
  }

  List<String> _getExclusions(CoverageType coverageType) {
    return [
      'War and terrorism',
      'Nuclear risks',
      'Intentional acts',
      'Prior known circumstances',
      'Unencrypted portable devices',
    ];
  }

  Map<String, double> _calculateDiscountsMap(RiskAssessment assessment) {
    final discounts = <String, double>{};

    if (assessment.complianceRisk.score < 20) {
      discounts['GDPR Compliance'] = 0.10;
    }

    if (assessment.systemRisk.score < 30) {
      discounts['Strong Security Controls'] = 0.15;
    }

    return discounts;
  }

  Map<String, dynamic> _getComprehensiveCoverageDetails() {
    return {
      'dataBreachResponse': {
        'limit': 500000,
        'includes': ['Notification costs', 'Credit monitoring', 'Legal fees'],
      },
      'businessInterruption': {
        'limit': 1000000,
        'waitingPeriod': '8 hours',
        'maxPeriod': '12 months',
      },
      'cyberExtortion': {
        'limit': 250000,
        'includes': ['Ransom payments', 'Negotiation costs'],
      },
      'thirdPartyLiability': {
        'limit': 2000000,
        'includes': ['Privacy liability', 'Network security liability'],
      },
      'regulatoryFines': {
        'limit': 1000000,
        'includes': ['GDPR fines', 'Other regulatory penalties'],
      },
    };
  }

  List<String> _getStandardExclusions() {
    return [
      'War, invasion, acts of foreign enemies',
      'Nuclear risks and radioactive contamination',
      'Intentional or criminal acts by insured',
      'Prior known circumstances',
      'Unencrypted portable devices and media',
      'Failure to implement reasonable security measures',
      'Bodily injury and property damage',
      'Employment practices liability',
    ];
  }

  List<String> _identifyCoverageGaps() {
    final gaps = <String>[];

    final activePolicies = _policies.where(
      (p) => p.status == PolicyStatus.active,
    );

    if (activePolicies.isEmpty) {
      gaps.add('No active cyber insurance coverage');
    } else {
      final totalCoverage = activePolicies.fold<double>(
        0,
        (sum, p) => sum + p.coverageLimit,
      );
      if (totalCoverage < 1000000) {
        gaps.add('Insufficient coverage limit for business size');
      }
    }

    return gaps;
  }

  List<String> _generateCoverageRecommendations() {
    return [
      'Consider increasing coverage limits based on business growth',
      'Review policy terms annually',
      'Ensure all subsidiaries are covered',
      'Consider cyber business interruption coverage',
      'Implement recommended security controls for premium discounts',
    ];
  }

  DateTime? _getNextRenewalDate() {
    final activePolicies = _policies.where(
      (p) => p.status == PolicyStatus.active,
    );
    if (activePolicies.isEmpty) return null;

    return activePolicies
        .map((p) => p.endDate)
        .reduce((a, b) => a.isBefore(b) ? a : b);
  }

  bool _checkInsuranceCompliance() {
    // Kontrola, zda máme dostatečné pokrytí
    final activePolicies = _policies.where(
      (p) => p.status == PolicyStatus.active,
    );
    return activePolicies.isNotEmpty;
  }

  /// Načítání a ukládání dat
  Future<void> _loadInsurancePolicies() async {
    // Load from storage
  }

  Future<void> _loadRiskAssessments() async {
    // Load from storage
  }

  Future<void> _initializeCoverageMonitoring() async {
    // Initialize monitoring
  }

  Future<void> _savePolicies() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'insurance_policies',
        jsonEncode(_policies.map((p) => p.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání policies: $e');
    }
  }

  Future<void> _saveClaims() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'insurance_claims',
        jsonEncode(_claims.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání claims: $e');
    }
  }

  Future<void> _saveRiskAssessments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'risk_assessments',
        jsonEncode(_riskAssessments.map((r) => r.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání risk assessments: $e');
    }
  }

  Future<void> _createDefaultPolicies() async {
    // Create sample policy for demonstration
    final samplePolicy = InsurancePolicy(
      id: 'demo_policy_001',
      quoteId: 'demo_quote_001',
      policyNumber: 'CYB-2024-DEMO',
      insurer: 'CyberGuard Insurance Ltd.',
      coverageType: CoverageType.comprehensive,
      coverageLimit: 1000000.0,
      annualPremium: 15000.0,
      deductible: 5000.0,
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 365)),
      status: PolicyStatus.active,
      companyDetails: {
        'name': 'Croatia Travel App s.r.o.',
        'address': 'Praha, Czech Republic',
        'industry': 'Technology/Software',
      },
      coverageDetails: _getComprehensiveCoverageDetails(),
      exclusions: _getStandardExclusions(),
      claims: [],
      lastPremiumPaid: DateTime.now(),
      nextPremiumDue: DateTime.now().add(const Duration(days: 365)),
    );

    _policies.add(samplePolicy);
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<InsurancePolicy> get policies => List.unmodifiable(_policies);
  List<InsuranceClaim> get claims => List.unmodifiable(_claims);
  List<RiskAssessment> get riskAssessments =>
      List.unmodifiable(_riskAssessments);
}
