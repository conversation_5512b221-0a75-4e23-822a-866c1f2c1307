import 'package:json_annotation/json_annotation.dart';

part 'cyber_insurance.g.dart';

/// 🛡️ CYBER INSURANCE MODELS - Modely pro cyber pojištění

/// Typ pokrytí
enum CoverageType { basic, standard, comprehensive, enterprise }

/// Status pojistky
enum PolicyStatus { active, inactive, expired, cancelled, suspended }

/// Úroveň rizika
enum RiskLevel { low, medium, high, critical, unknown }

/// Dopad rizika
enum RiskImpact { low, medium, high, critical }

/// Typ události pojištění
enum InsuranceEventType {
  riskAssessmentCompleted,
  quoteGenerated,
  policyPurchased,
  claimFiled,
  claimProcessed,
  policyRenewed,
  coverageUpdated,
}

/// Typ škody
enum ClaimType {
  dataBreach,
  cyberAttack,
  systemFailure,
  businessInterruption,
  thirdPartyLiability,
  regulatoryFines,
  forensicInvestigation,
}

/// Status škody
enum ClaimStatus { submitted, underReview, approved, denied, settled, closed }

/// Rizikový faktor
@JsonSerializable()
class RiskFactor {
  final String category;
  final double score;
  final List<String> factors;
  final RiskImpact impact;

  const RiskFactor({
    required this.category,
    required this.score,
    required this.factors,
    required this.impact,
  });

  factory RiskFactor.fromJson(Map<String, dynamic> json) =>
      _$RiskFactorFromJson(json);
  Map<String, dynamic> toJson() => _$RiskFactorToJson(this);
}

/// Risk assessment
@JsonSerializable()
class RiskAssessment {
  final String id;
  final DateTime conductedAt;
  final double overallRiskScore;
  final RiskLevel riskLevel;
  final RiskFactor dataRisk;
  final RiskFactor systemRisk;
  final RiskFactor complianceRisk;
  final RiskFactor businessRisk;
  final RiskFactor thirdPartyRisk;
  final List<String> recommendations;
  final double estimatedAnnualLoss;
  final DateTime nextAssessmentDue;

  const RiskAssessment({
    required this.id,
    required this.conductedAt,
    required this.overallRiskScore,
    required this.riskLevel,
    required this.dataRisk,
    required this.systemRisk,
    required this.complianceRisk,
    required this.businessRisk,
    required this.thirdPartyRisk,
    required this.recommendations,
    required this.estimatedAnnualLoss,
    required this.nextAssessmentDue,
  });

  factory RiskAssessment.fromJson(Map<String, dynamic> json) =>
      _$RiskAssessmentFromJson(json);
  Map<String, dynamic> toJson() => _$RiskAssessmentToJson(this);
}

/// Insurance quote
@JsonSerializable()
class InsuranceQuote {
  final String id;
  final String riskAssessmentId;
  final CoverageType coverageType;
  final double coverageLimit;
  final double annualPremium;
  final double deductible;
  final Duration policyTerm;
  final DateTime validUntil;
  final Map<String, dynamic> coverageDetails;
  final List<String> exclusions;
  final List<RiskFactor> riskFactors;
  final Map<String, double> discounts;
  final DateTime createdAt;

  const InsuranceQuote({
    required this.id,
    required this.riskAssessmentId,
    required this.coverageType,
    required this.coverageLimit,
    required this.annualPremium,
    required this.deductible,
    required this.policyTerm,
    required this.validUntil,
    required this.coverageDetails,
    required this.exclusions,
    required this.riskFactors,
    required this.discounts,
    required this.createdAt,
  });

  factory InsuranceQuote.fromJson(Map<String, dynamic> json) =>
      _$InsuranceQuoteFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceQuoteToJson(this);
}

/// Insurance policy
@JsonSerializable()
class InsurancePolicy {
  final String id;
  final String quoteId;
  final String policyNumber;
  final String insurer;
  final CoverageType coverageType;
  final double coverageLimit;
  final double annualPremium;
  final double deductible;
  final DateTime startDate;
  final DateTime endDate;
  final PolicyStatus status;
  final Map<String, dynamic> companyDetails;
  final Map<String, dynamic> coverageDetails;
  final List<String> exclusions;
  final List<String> claims;
  final DateTime lastPremiumPaid;
  final DateTime nextPremiumDue;

  const InsurancePolicy({
    required this.id,
    required this.quoteId,
    required this.policyNumber,
    required this.insurer,
    required this.coverageType,
    required this.coverageLimit,
    required this.annualPremium,
    required this.deductible,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.companyDetails,
    required this.coverageDetails,
    required this.exclusions,
    required this.claims,
    required this.lastPremiumPaid,
    required this.nextPremiumDue,
  });

  InsurancePolicy copyWith({
    String? id,
    String? quoteId,
    String? policyNumber,
    String? insurer,
    CoverageType? coverageType,
    double? coverageLimit,
    double? annualPremium,
    double? deductible,
    DateTime? startDate,
    DateTime? endDate,
    PolicyStatus? status,
    Map<String, dynamic>? companyDetails,
    Map<String, dynamic>? coverageDetails,
    List<String>? exclusions,
    List<String>? claims,
    DateTime? lastPremiumPaid,
    DateTime? nextPremiumDue,
  }) {
    return InsurancePolicy(
      id: id ?? this.id,
      quoteId: quoteId ?? this.quoteId,
      policyNumber: policyNumber ?? this.policyNumber,
      insurer: insurer ?? this.insurer,
      coverageType: coverageType ?? this.coverageType,
      coverageLimit: coverageLimit ?? this.coverageLimit,
      annualPremium: annualPremium ?? this.annualPremium,
      deductible: deductible ?? this.deductible,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      companyDetails: companyDetails ?? this.companyDetails,
      coverageDetails: coverageDetails ?? this.coverageDetails,
      exclusions: exclusions ?? this.exclusions,
      claims: claims ?? this.claims,
      lastPremiumPaid: lastPremiumPaid ?? this.lastPremiumPaid,
      nextPremiumDue: nextPremiumDue ?? this.nextPremiumDue,
    );
  }

  factory InsurancePolicy.fromJson(Map<String, dynamic> json) =>
      _$InsurancePolicyFromJson(json);
  Map<String, dynamic> toJson() => _$InsurancePolicyToJson(this);
}

/// Insurance claim
@JsonSerializable()
class InsuranceClaim {
  final String id;
  final String policyId;
  final String claimNumber;
  final ClaimType claimType;
  final String incidentDescription;
  final DateTime incidentDate;
  final double estimatedLoss;
  final ClaimStatus status;
  final DateTime filedAt;
  final List<String> supportingDocuments;
  final Map<String, dynamic> additionalInfo;
  final List<ClaimUpdate> updates;

  const InsuranceClaim({
    required this.id,
    required this.policyId,
    required this.claimNumber,
    required this.claimType,
    required this.incidentDescription,
    required this.incidentDate,
    required this.estimatedLoss,
    required this.status,
    required this.filedAt,
    required this.supportingDocuments,
    required this.additionalInfo,
    required this.updates,
  });

  factory InsuranceClaim.fromJson(Map<String, dynamic> json) =>
      _$InsuranceClaimFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceClaimToJson(this);
}

/// Claim update
@JsonSerializable()
class ClaimUpdate {
  final String id;
  final DateTime timestamp;
  final String message;
  final ClaimStatus newStatus;
  final String updatedBy;

  const ClaimUpdate({
    required this.id,
    required this.timestamp,
    required this.message,
    required this.newStatus,
    required this.updatedBy,
  });

  factory ClaimUpdate.fromJson(Map<String, dynamic> json) =>
      _$ClaimUpdateFromJson(json);
  Map<String, dynamic> toJson() => _$ClaimUpdateToJson(this);
}

/// Insurance event
@JsonSerializable()
class InsuranceEvent {
  final InsuranceEventType type;
  final String? policyId;
  final String? claimId;
  final String? quoteId;
  final String? assessmentId;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const InsuranceEvent({
    required this.type,
    this.policyId,
    this.claimId,
    this.quoteId,
    this.assessmentId,
    required this.timestamp,
    this.data = const {},
  });

  factory InsuranceEvent.fromJson(Map<String, dynamic> json) =>
      _$InsuranceEventFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceEventToJson(this);
}

/// Coverage status
@JsonSerializable()
class CoverageStatus {
  final String policyId;
  final bool isActive;
  final DateTime lastChecked;
  final List<String> alerts;

  const CoverageStatus({
    required this.policyId,
    required this.isActive,
    required this.lastChecked,
    required this.alerts,
  });

  factory CoverageStatus.fromJson(Map<String, dynamic> json) =>
      _$CoverageStatusFromJson(json);
  Map<String, dynamic> toJson() => _$CoverageStatusToJson(this);
}

/// Coverage report
@JsonSerializable()
class CoverageReport {
  final DateTime reportDate;
  final int activePolicies;
  final double totalCoverageLimit;
  final double totalAnnualPremiums;
  final int openClaims;
  final RiskLevel currentRiskLevel;
  final List<String> coverageGaps;
  final List<String> recommendations;
  final DateTime? nextRenewalDate;
  final bool complianceStatus;

  const CoverageReport({
    required this.reportDate,
    required this.activePolicies,
    required this.totalCoverageLimit,
    required this.totalAnnualPremiums,
    required this.openClaims,
    required this.currentRiskLevel,
    required this.coverageGaps,
    required this.recommendations,
    this.nextRenewalDate,
    required this.complianceStatus,
  });

  factory CoverageReport.fromJson(Map<String, dynamic> json) =>
      _$CoverageReportFromJson(json);
  Map<String, dynamic> toJson() => _$CoverageReportToJson(this);
}
